import html
import json
import logging
import os
import time
from datetime import datetime
from typing import List, Dict
from zoneinfo import ZoneInfo

import boto3
from django.db import connection, reset_queries
from django.db.models import Sum, Count, Q, F, OuterRef, Subquery, Window
from django.utils import timezone, dateparse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.db.models.functions import RowNumber

from ColdEmailerBackend.settings import DEBUG
from mainapp.campaign import CampaignManager, CampaignCancelError
from mainapp.decorators import staff_only
from mainapp.email_messages import approval_request_passed_email_message, approval_request_rejected_email_message
from mainapp.models import User, SpamWord, FriendlySalutation, ManagedSubdomain, Campaign, EmailSubdomain, EmailID, \
    SubscriptionPlan, FreePlanIntegrityError, CampaignEmailMessage, BlacklistCheckStatus, UpdateSection, WarmupSchedule, \
    CampaignSchedule, UnsubscribedEmail, CampaignActivity
from mainapp.responses import JsonResponseSuccess, JsonResponseForbidden, JsonResponseBadRequest, JsonResponseNotFound
from mainapp.serializers import SpamWordsSerializer, SalutationWordsSerializer, UpdateSectionSerializer, AdminCampaignSerializer
from mainapp.tasks import cancel_all_campaigns_task, send_email_task, cancel_campaign_task, \
    delete_managed_subdomain_task

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_auth(request):
    """
    Checks if the authenticated user in the request is an Admin user.
    If not returns 403 Forbidden response.
    """
    user: User = request.user
    if user.is_admin:
        return JsonResponseSuccess(data={
            "email": user.email
        })
    else:
        return JsonResponseForbidden()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_dashboard(_request):
    """
    Admin API for campaign message approval feature and key statistics dashboard.
    """
    now = timezone.now()

    # Total domains
    total_domains = ManagedSubdomain.objects.count()

    # Total users
    total_users = User.objects.count()

    plan_stats = User.objects.aggregate(
        paid_users=Count('id', filter=Q(active_workspace__subscription_plan__is_free_plan=False)),
        free_users=Count('id', filter=Q(active_workspace__subscription_plan__is_free_plan=True))
    )

    warmup_emails_sent_today = WarmupSchedule.objects.filter(
        schedule_datetime__date=now.date()
    ).count()

    # Total campaign emails sent today (based on sent_on timestamp)
    campaign_emails_sent_today = CampaignSchedule.objects.filter(
        sent_on__isnull=False, sent_on__date=now.date()
    ).count()

    total_email_addresses = EmailID.objects.count()

    active_campaigns = Campaign.objects.filter(status__iexact="running").count()

    total_unsubscribed_emails = UnsubscribedEmail.objects.count()

    total_replies_received = CampaignSchedule.objects.filter(reply_received=True).count()

    total_signup_user_today = User.objects.filter(date_joined__date=now.date()).count()

    total_domains_blacklisted_today = BlacklistCheckStatus.objects.filter(
        checked_on=now.date()
    ).exclude(blacklisted_sources=[]) \
     .count()

    total_domains_blacklisted = BlacklistCheckStatus.objects.exclude(
        blacklisted_sources=[]
    ).count()

    ses_client = boto3.client('ses', region_name=os.environ["CE_AWS_REGION"])
    identities = []
    next_token = None

    while True:
        if next_token:
            response = ses_client.list_identities(NextToken=next_token, MaxItems=1000)
        else:
            response = ses_client.list_identities(MaxItems=1000)

        identities.extend(response.get("Identities", []))
        next_token = response.get("NextToken")

        if not next_token:
            break

    total_aws_identities = len(identities)

    return JsonResponseSuccess(data={
        "total_domains": total_domains,
        "total_users": total_users,
        "paid_users": plan_stats["paid_users"],
        "free_users": plan_stats["free_users"],
        "warmup_emails_sent_today": warmup_emails_sent_today,
        "campaign_emails_sent_today": campaign_emails_sent_today,
        "total_email_addresses": total_email_addresses,
        "active_campaigns": active_campaigns,
        "total_unsubscribed_emails": total_unsubscribed_emails,
        "total_replies_received": total_replies_received,
        "total_signup_user_today": total_signup_user_today,
        "total_domains_blacklisted_today": total_domains_blacklisted_today,
        "total_domains_blacklisted": total_domains_blacklisted,
        "total_aws_identities": total_aws_identities
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_all_users_page(_request):
    """
    Handles requests for admin all users page.
    """
    user_data = []
    ist_timezone = ZoneInfo("Asia/Kolkata")

    for user in User.objects.all().order_by("-date_joined"):
        print(int(user.date_joined.timestamp() * 1000))
        plan_name = user.active_workspace.subscription_plan.plan_name
        user_data.append({
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "date_joined_ts": int(user.date_joined.astimezone(ist_timezone).timestamp() * 1000),
            "total_domains": ManagedSubdomain.objects.filter(workspace__user=user).count(),
            "total_campaigns": Campaign.objects.filter(workspace__user=user).count(),
            "last_activity": int(user.last_activity_time.astimezone(ist_timezone).timestamp() * 1000),
            "plan_name": plan_name,
        })

    return JsonResponseSuccess(data={
        "users": user_data
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_details(request):
    """
    Handles requests for admin all users page.
    """
    try:
        user_id: int = request.query_params["user_id"]
    except KeyError as k:
        logger.critical(f"admin_all_users_page() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_all_users_page() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": "Could not find resource on server"
        })

    cancelled_due_to_bounce_count = Campaign.objects.filter(
            workspace__user=user,
            is_campaign_bounce_blocked=True
        ).count()

    workspace_details = [
        {
            "id": ws.id,
            "name": ws.name,
        }
        for ws in user.workspace_set.all()
    ]

    return JsonResponseSuccess(data={
        "email": user.email,
        "username": user.username,
        "user_id": user.id,
        "total_workspaces": user.workspace_set.count(),
        "total_connected_domains": ManagedSubdomain.objects.filter(workspace__user=user).count(),
        "total_email_subdomains": EmailSubdomain.objects.filter(managed_subdomain__workspace__user=user).count(),
        "total_email_ids": EmailID.objects.filter(email_subdomain__managed_subdomain__workspace__user=user).count(),
        "total_campaigns": Campaign.objects.filter(workspace__user=user).count(),
        "total_emails_sent": user.workspace_set.aggregate(Sum("total_emails_sent"))["total_emails_sent__sum"],
        "monthly_total_emails_remaining": sum(ws.credits_remaining for ws in user.workspace_set.all()),
        "campaigns_blocked": user.is_campaign_blocked,
        "user_verified": user.email_verified,
        "cancelled_due_to_bounce_count": cancelled_due_to_bounce_count,
        "workspace_details" : workspace_details
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_workspaces(request):
    """
    API for admin user details "workspaces" page.
    """
    try:
        user_id: int = request.query_params["user_id"]
    except KeyError as k:
        logger.critical(f"admin_user_workspaces() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_user_workspaces() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": "Could not find resource on server"
        })

    workspaces: List[Dict] = []
    for workspace in user.workspace_set.all().order_by("-created_on"):
        workspaces.append({
            "id": workspace.id,
            "name": workspace.name,
            "created_on_ts": int(workspace.created_on.timestamp() * 1000),
            "total_emails_sent": workspace.total_emails_sent,
            "total_replies_received": workspace.total_replies_received,
        })

    return JsonResponseSuccess(data={
        "user_email": user.email,
        "workspaces": workspaces,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_connected_domains(request):
    """
    API for admin user details "connected domains" page.
    """
    try:
        user_id: int = request.query_params["user_id"]
    except KeyError as k:
        logger.critical(f"admin_user_connected_domains() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_user_connected_domains() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": "Could not find resource on server"
        })

    connected_domains: List[Dict] = []
    for msub in ManagedSubdomain.objects.filter(workspace__user=user).order_by("-created_on"):
        blacklist_entry = (BlacklistCheckStatus.objects.filter(subdomain=msub).order_by("-checked_on").first())

        if blacklist_entry is None:
            blacklist_status = "Incomplete"
        elif not blacklist_entry.blacklisted_sources:
            blacklist_status = "Healthy"
        elif any("Unknown error" in s for s in blacklist_entry.blacklisted_sources):
            blacklist_status = "Unknown Error"
        else:
            blacklist_status = ", ".join(blacklist_entry.blacklisted_sources)

        # Fetch any active campaigns usign this domain.
        camps: List[str] = list(msub.campaign_set.exclude(
            status__in=["creating", "created", "cancelled", "complete"]
        ).values_list("name", flat=True))

        connected_domains.append({
            "id": msub.id,
            "domain": msub.subdomain,
            "active_campaigns": camps,
            "created_on_ts": int(msub.created_on.timestamp() * 1000),
            "setup_complete": msub.setup_complete,
            "current_setup_stage": msub.current_setup_stage,
            "naming_strategy": msub.naming_strategy,
            "aws_hosted_zone_id": msub.hosted_zone_id,
            "sending_limit": msub.email_limit,
            "warmup_email_count": msub.warmup_email_count,
            "seconds_remaining": max(int((msub.domain_usable_from - timezone.now()).total_seconds()), 0),
            "redirect_subdomains_to": msub.redirect_subdomains_to,
            "blacklist_status": blacklist_status,
            "workspace_name": msub.workspace.name
        })

    return JsonResponseSuccess(data={
        "user_email": user.email,
        "connected_domains": connected_domains,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_delete_connected_domain(request):
    """
    Admin API for deleting connected domains.
    """
    try:
        managed_subdomain_id: str = request.data["id"]
    except KeyError as k:
        logger.critical(f"admin_delete_connected_domain() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        msub = ManagedSubdomain.objects.get(id=managed_subdomain_id)
    except ManagedSubdomain.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find this domain on server"
        })

    # Fetch all running campaigns using this domain and cancel them.
    active_campaigns = msub.campaign_set.exclude(
        status__in=["creating", "created", "cancelled", "complete"]
    )
    for camp in active_campaigns:
        logger.debug(f"admin_delete_connected_domain() - Cancelling campaign {camp.uid}...")
        # Start celery task.
        cancel_campaign_task.delay(camp.uid)

        # We'll manually change status here so that user sees it as cancelled immediately.
        camp.is_deleting_schedules = True
        camp.scheduled_start_datetime = None
        camp.status = "cancelled"
        camp.save()

    # Delete the domain.
    logger.debug(f"Starting celery task for deleting domain {msub.subdomain}")
    delete_managed_subdomain_task.delay(msub.subdomain)

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_emails(request):
    """
    API for admin user details "emails" page.
    """
    try:
        user_id: int = request.query_params["user_id"]
    except KeyError as k:
        logger.critical(f"admin_user_emails() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_user_emails() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": "Could not find resource on server"
        })

    emails: List[Dict] = []
    for email in EmailID.objects.filter(email_subdomain__managed_subdomain__workspace__user=user).order_by(
            "email_address"
    ):
        emails.append({
            "id": email.id,
            "address": email.email_address,
            "username": email.username,
            "subdomain": email.email_subdomain.subdomain,
        })

    return JsonResponseSuccess(data={
        "user_email": user.email,
        "emails": emails,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_campaigns(request):
    """
    API for admin user details "campaigns" page.
    """
    try:
        user_id: int = request.query_params["user_id"]
    except KeyError as k:
        logger.critical(f"admin_user_campaigns() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_user_campaigns() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": "Could not find resource on server"
        })

    campaigns: List[Dict] = []
    for camp in Campaign.objects.filter(workspace__user=user).order_by("-created_on"):
        if camp.scheduled_start_datetime is not None:
            schedule_ts = int(camp.scheduled_start_datetime.timestamp() * 1000)
        else:
            schedule_ts = None

        total_replies = CampaignSchedule.objects.filter(
                campaign=camp,
                reply_received=True
            ).count()

        total_sent = CampaignSchedule.objects.filter(
            campaign=camp,
            sent_on__isnull=False
        ).count()

        campaigns.append({
            "uid": camp.uid,
            "workspace": camp.workspace.name,
            "created_on_ts": int(camp.created_on.timestamp() * 1000),
            "name": camp.name,
            "custom_emails_per_day": camp.emails_per_day,
            "status": camp.status,
            "scheduled_start_datetime_ts": schedule_ts,
            "reply_to_address": camp.reply_to_address,
            "sending_domains": list(camp.sending_domains.all().values_list("subdomain", flat=True)),
            "bounces": camp.bounces,
            "total_replies": total_replies,
            "total_sent": total_sent,
        })

    return JsonResponseSuccess(data={
        "user_email": user.email,
        "campaigns": campaigns,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_campaign_details(request):
    """
    API for admin user details "campaign details" page.
    """
    try:
        user_id: int = int(request.query_params["user_id"])
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"admin_user_campaign_details() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_user_campaign_details() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": f"Could not find user with id {user_id}"
        })

    try:
        campaign = Campaign.objects.get(workspace__user=user, uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"admin_user_campaign_details() - Campaign with uid {campaign_uid} does not exists with "
                     f"user {user.email}.")
        return JsonResponseBadRequest(data={
            "message": f"Could not find campaign with uid {campaign_uid} on user {user.email}"
        })

    messages: List[Dict] = []
    for message in campaign.campaignemailmessage_set.all().order_by("order"):
        messages.append({
            "uid": message.uid,
            "subject": message.subject,
            "body": message.body,
            "next_message_days": message.next_message_days,
            "spam_score": message.score,
            "verdict": message.verdict,
            "suggestions": message.suggestions,
        })

    contacts: List[Dict] = []
    for contact in campaign.campaigncontact_set.prefetch_related("sending_email").all():
        contacts.append({
            "uid": contact.uid,
            "email_id": contact.email_id,
            "attriutes": json.dumps(contact.attributes),
            "sending_email": contact.sending_email.email_address if contact.sending_email else None,
        })

    schedules: List[Dict] = []
    for schedule in campaign.campaignschedule_set.all():
        schedules.append({
            "uid": schedule.uid,
            "contact": schedule.contact.email_id,
            "message_uid": schedule.email_message.uid,
            "schedule_date_ts": int(
                schedule.schedule_datetime.timestamp() * 1000) if schedule.schedule_datetime else None,
            "status": schedule.status,
            "sent_on_ts": int(schedule.sent_on.timestamp() * 1000) if schedule.sent_on else None,
            "aws_message_id": schedule.message_id,
            "email_s3_key": schedule.email_s3_key,
            "reply_received": schedule.reply_received,
            "reply_classification": schedule.reply_classification,
        })

    return JsonResponseSuccess(data={
        "user_email": user.email,
        "campaign_name": campaign.name,
        "messages": messages,
        "contacts": contacts,
        "schedules": schedules,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_cancel_campaign(request):
    """
    Admin API to cancel given campaign.
    """
    try:
        campaign_uid: str = request.data["uid"]
    except KeyError as k:
        logger.critical(f"admin_cancel_campaign() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        camp: Campaign = Campaign.objects.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": f"Could not find campaign with uid {campaign_uid}"
        })

    cm = CampaignManager(camp)

    try:
        cm.stop_campaign()
    except CampaignCancelError as err:
        logger.error(f"{err}")
        return JsonResponseBadRequest(data={"message": "This campaign cannot be cancelled in current state."})

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_get_spam_words(_request):
    """
    API to fetch all spam word data.
    """
    return JsonResponseSuccess(data={"spam_words": SpamWordsSerializer(SpamWord.objects.all(), many=True).data})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_delete_spam_word(request):
    """
    API to delete a single spam word.
    """
    try:
        spam_word_id: int = request.data["spam_word_id"]
    except KeyError as k:
        logger.critical(f"Missing key in request {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key in request {k}"
        })

    try:
        SpamWord.objects.get(id=spam_word_id).delete()
    except SpamWord.DoesNotExist:
        return JsonResponseBadRequest(data={
            "message": "Could not find spam word in Database."
        })

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_delete_spam_words(request):
    """
    API to delete multiple spam words.
    """
    try:
        spam_word_ids: List[int] = request.data["spam_word_ids"]
    except KeyError as k:
        logger.critical(f"Missing key in request {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key in request {k}"
        })

    SpamWord.objects.filter(pk__in=spam_word_ids).delete()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_add_spam_words(request):
    """
    API to add spam words.
    """
    try:
        spam_words: List[str] = request.data["spam_words"]
    except KeyError as k:
        logger.critical(f"Missing key in request {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key in request {k}"
        })

    bulk_ops = []
    for word in spam_words:
        bulk_ops.append(SpamWord(name=word.strip().lower()))

    SpamWord.objects.bulk_create(bulk_ops, ignore_conflicts=True)

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_get_salutation_words(_request):
    """
    API to fetch all salutation word data.
    """
    return JsonResponseSuccess(data={
        "salutation_words": SalutationWordsSerializer(FriendlySalutation.objects.all(), many=True).data
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_delete_salutation_word(request):
    """
    API to delete a single salutation word.
    """
    try:
        salutation_word_id: int = request.data["salutation_word_id"]
    except KeyError as k:
        logger.critical(f"Missing key in request {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key in request {k}"
        })

    try:
        FriendlySalutation.objects.get(id=salutation_word_id).delete()
    except FriendlySalutation.DoesNotExist:
        return JsonResponseBadRequest(data={
            "message": "Could not find salutation word in Database."
        })

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_delete_salutation_words(request):
    """
    API to delete multiple salutation words.
    """
    try:
        salutation_word_ids: List[int] = request.data["salutation_word_ids"]
    except KeyError as k:
        logger.critical(f"Missing key in request {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key in request {k}"
        })

    FriendlySalutation.objects.filter(pk__in=salutation_word_ids).delete()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_add_salutation_words(request):
    """
    API to add salutation words.
    """
    try:
        salutation_words: List[str] = request.data["salutation_words"]
    except KeyError as k:
        logger.critical(f"Missing key in request {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key in request {k}"
        })

    bulk_ops = []
    for word in salutation_words:
        bulk_ops.append(FriendlySalutation(name=word.strip().lower()))

    FriendlySalutation.objects.bulk_create(bulk_ops, ignore_conflicts=True)

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_block_user_campaigns(request):
    """
    API to block user camapign creation and cancel all current campaigns.
    """
    try:
        user_id: int = request.data["id"]
    except KeyError as k:
        logger.critical(f"admin_block_user_campaigns() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user: User = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_block_user_campaigns() - No user with id {user_id} exists.")
        return JsonResponseBadRequest(data={
            "message": f"No user with id '{user_id}' exists"
        })

    # Mark account as blocked.
    logger.info(f"Blocking account {user.email}")
    user.is_campaign_blocked = True
    user.save()

    # Cancel all running campaigns.
    logger.debug(f"Starting task for cancelling all camapigns for user {user.email}")
    cancel_all_campaigns_task.delay(user.id)

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_unblock_user_campaigns(request):
    """
    API to unblock user from creating new campaigns.
    """
    try:
        user_id: int = request.data["id"]
    except KeyError as k:
        logger.critical(f"admin_block_user_campaigns() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user: User = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_block_user_campaigns() - No user with id {user_id} exists.")
        return JsonResponseBadRequest(data={
            "message": f"No user with id '{user_id}' exists"
        })

    logger.debug(f"Unblocking campaign creation for account {user.email}")

    user.is_campaign_blocked = False
    user.save()

    logger.debug(f"Account {user.email} has been unblocked from campaign creation.")

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_all_subscription_plans(_request):
    """
    API for admin subscriptipn plans page.
    """
    plans: List[Dict] = []
    for plan in SubscriptionPlan.objects.all():
        plans.append({
            "id": plan.id,
            "name": plan.plan_name,
            "created_on_ts": plan.created_on,
            "monthly_amount": plan.monthly_amount,
            "annual_amount": plan.annual_amount,
            "popular": plan.popular,
            "hidden": plan.hidden,
            "is_free_plan": plan.is_free_plan,
            "monthly_email_sending_quota": plan.monthly_email_sending_quota,
        })

    return JsonResponseSuccess(data={
        "plans": plans
    })


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_create_new_subscription_plan(request):
    """
    Admin API for creating new subscription plan (on deliveryman).
    """
    if request.method == "GET":
        return JsonResponseSuccess()

    else:
        try:
            is_free_plan: bool = request.data["is_free_plan"]
            plan_display_name: str = html.escape(request.data["plan_display_name"].strip())
            display_order: int = int(request.data["display_order"].strip())
            product_id: str | None = html.escape(request.data["product_id"].strip()) or None
            monthly_price_id: str | None = html.escape(request.data["monthly_price_id"].strip()) or None
            annual_price_id: str | None = html.escape(request.data["annual_price_id"].strip()) or None
            monthly_amount: int = int(request.data["monthly_amount"].strip())
            annual_amount: int = int(request.data["annual_amount"].strip())
            monthly_feature_list: str = html.escape(request.data["monthly_feature_list"].strip())
            annual_feature_list: str = html.escape(request.data["annual_feature_list"].strip())
            monthly_email_sending_quota: int = int(request.data["monthly_email_sending_quota"].strip())
            annual_email_sending_quota: int = int(request.data["annual_email_sending_quota"].strip())
            total_domain_connections_allowed: int = int(request.data["total_domain_connections_allowed"].strip())
            popular_plan: bool = request.data["popular_plan"]
            hidden: bool = request.data["hidden"]
        except KeyError as k:
            logger.critical(f"admin_create_new_subscription_plan() - Missing key {k}")
            return JsonResponseBadRequest(data={
                "message": f"Missing key {k}"
            })

        try:
            new_plan = SubscriptionPlan.objects.create(
                is_free_plan=is_free_plan,
                plan_name=plan_display_name,
                display_order=display_order,
                product_id=product_id,
                monthly_price_id=monthly_price_id,
                annual_price_id=annual_price_id,
                monthly_amount=monthly_amount,
                annual_amount=annual_amount,
                monthly_feature_list=monthly_feature_list.split("\n"),
                annual_feature_list=annual_feature_list.split("\n"),
                popular=popular_plan,
                hidden=hidden,
                monthly_email_sending_quota=monthly_email_sending_quota,
                annual_email_sending_quota=annual_email_sending_quota,
                total_domain_connections_allowed=total_domain_connections_allowed,
            )
        except FreePlanIntegrityError as err:
            return JsonResponseBadRequest(data={
                "message": f"{err}"
            })

        logger.debug(f"New plan {plan_display_name} ({new_plan.id}) has been created successfully!")

        return JsonResponseSuccess()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_edit_subscription_plan(request):
    """
    Admin API for editing subscription plan (on deliveryman).
    """
    if request.method == "GET":
        try:
            plan_id: str = request.query_params["plan_id"]
        except KeyError as k:
            logger.critical(f"admin_edit_subscription_plan() - Missing query param {k}")
            return JsonResponseBadRequest(data={
                "message": f"Missing query param {k}"
            })

        try:
            plan = SubscriptionPlan.objects.get(id=plan_id)
        except SubscriptionPlan.DoesNotExist:
            return JsonResponseNotFound(data={
                "message": f"Could not find subscription plan with id {plan_id} on server."
            })

        return JsonResponseSuccess(data={
            "is_free_plan": plan.is_free_plan,
            "plan_display_name": plan.plan_name,
            "display_order": plan.display_order,
            "product_id": plan.product_id,
            "monthly_price_id": plan.monthly_price_id,
            "annual_price_id": plan.annual_price_id,
            "monthly_amount": plan.monthly_amount,
            "annual_amount": plan.annual_amount,
            "monthly_feature_list": plan.monthly_feature_list,
            "annual_feature_list": plan.annual_feature_list,
            "monthly_email_sending_quota": plan.monthly_email_sending_quota,
            "annual_email_sending_quota": plan.annual_email_sending_quota,
            "total_domain_connections_allowed": plan.total_domain_connections_allowed,
            "popular_plan": plan.popular,
            "hidden": plan.hidden,
        })

    else:
        try:
            is_free_plan: bool = request.data["is_free_plan"]
            plan_id: str = request.data["plan_id"].strip()
            plan_display_name: str = html.escape(request.data["plan_display_name"].strip())
            display_order: int = int(request.data["display_order"].strip())
            product_id: str | None = html.escape(request.data["product_id"].strip()) or None
            monthly_price_id: str | None = html.escape(request.data["monthly_price_id"].strip()) or None
            annual_price_id: str | None = html.escape(request.data["annual_price_id"].strip()) or None
            monthly_amount: int = int(request.data["monthly_amount"].strip())
            annual_amount: int = int(request.data["annual_amount"].strip())
            monthly_feature_list: str = html.escape(request.data["monthly_feature_list"].strip())
            annual_feature_list: str = html.escape(request.data["annual_feature_list"].strip())
            monthly_email_sending_quota: int = int(request.data["monthly_email_sending_quota"].strip())
            annual_email_sending_quota: int = int(request.data["annual_email_sending_quota"].strip())
            total_domain_connections_allowed: int = int(request.data["total_domain_connections_allowed"].strip())
            popular_plan: bool = request.data["popular_plan"]
            hidden: bool = request.data["hidden"]
        except KeyError as k:
            logger.critical(f"admin_create_new_subscription_plan() - Missing key {k}")
            return JsonResponseBadRequest(data={
                "message": f"Missing key {k}"
            })

        try:
            plan = SubscriptionPlan.objects.get(id=plan_id)
        except SubscriptionPlan.DoesNotExist:
            return JsonResponseNotFound(data={
                "message": f"Could not find subscription plan with id {plan_id} on server."
            })

        try:
            plan.is_free_plan = is_free_plan
            plan.plan_name = plan_display_name
            plan.display_order = display_order
            plan.product_id = product_id
            plan.monthly_price_id = monthly_price_id
            plan.annual_price_id = annual_price_id
            plan.monthly_amount = monthly_amount
            plan.annual_amount = annual_amount
            plan.monthly_feature_list = monthly_feature_list.split("\n")
            plan.annual_feature_list = annual_feature_list.split("\n")
            plan.monthly_email_sending_quota = monthly_email_sending_quota
            plan.annual_email_sending_quota = annual_email_sending_quota
            plan.total_domain_connections_allowed = total_domain_connections_allowed
            plan.popular = popular_plan
            plan.hidden = hidden
            plan.save()
        except FreePlanIntegrityError as err:
            return JsonResponseBadRequest(data={
                "message": f"{err}"
            })

        logger.debug(f"All changes saved for plan {plan_display_name} ({plan_id})")

        return JsonResponseSuccess()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_approval_request(request):
    """
    Admin API for campaign message approval feature.
    """
    if request.method == "GET":
        all_pending_requests = []
        for email_message in CampaignEmailMessage.objects.filter(is_flagged_for_approval=True):
            all_pending_requests.append({
                "uid": email_message.uid,
                "subject": email_message.subject,
                "body": email_message.body,
                "blocked_reason": email_message.blocked_reason,
                "user_email": (
                    email_message.campaign.workspace.user.email
                    if email_message.campaign
                    and email_message.campaign.workspace
                    and email_message.campaign.workspace.user
                    else None
                ),
            })

        return JsonResponseSuccess(data={
            "pending_requests": all_pending_requests,
        })

    else:
        try:
            message_uid: str = request.data["message_uid"]
            approved: bool = request.data["approved"]
        except KeyError as k:
            logger.error(f"admin_approval_request() - Missing key {k}")
            return JsonResponseBadRequest(data={
                "message": f"Missing key {k}"
            })

        try:
            email_message = CampaignEmailMessage.objects.get(uid=message_uid)
        except CampaignEmailMessage.DoesNotExist:
            return JsonResponseNotFound(data={
                "message": f"Could not find message with id {message_uid} on server."
            })

        if approved:
            email_message.score = 100

        email_message.label = "approved" if approved else "rejected"
        email_message.is_flagged_for_approval = False
        email_message.save()

        # Send emails to user.
        campaign: Campaign = email_message.campaign
        user: User = email_message.campaign.workspace.user
        if approved:
            send_email_task.delay(
                to=user.email,
                sender="<EMAIL>",
                sender_name="Team DeliverymanAI",
                subject=f"Your campaign content is approved",
                body_html=approval_request_passed_email_message(
                    username=user.username,
                    campaign_name=campaign.name,
                    campaign_dashboard_url=os.environ["CE_APP_HOST_URL"] + f"/campaigns/{campaign.uid}/",
                    current_year=str(datetime.now().year),
                )
            )
        else:
            send_email_task.delay(
                to=user.email,
                sender="<EMAIL>",
                sender_name="Team DeliverymanAI",
                subject=f"Your campaign content is rejected",
                body_html=approval_request_rejected_email_message(
                    username=user.username,
                    campaign_name=campaign.name,
                    campaign_dashboard_url=os.environ["CE_APP_HOST_URL"] + f"/campaigns/{campaign.uid}/",
                    current_year=str(datetime.now().year),
                )
            )

        return JsonResponseSuccess(data={
            "message_uid": message_uid,
            "approved": approved,
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@staff_only
def admin_add_update_post(request):
    """
    Adds the Update post.
    :param request: Django Rest Framework's Request object.
    """

    try:
        title: str = request.data['title']
        description: str = request.data['description']
        datetime_string = request.data['date']
    except KeyError as k:
        logger.error(f"admin_add_update_post() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        formatted_datetime = datetime.fromisoformat(datetime_string)
        if timezone.is_naive(formatted_datetime):
            formatted_datetime = timezone.make_aware(formatted_datetime)

        UpdateSection.objects.create(title=title, description=description, created_at=formatted_datetime)

        return JsonResponseSuccess(
            {'data': UpdateSectionSerializer(UpdateSection.objects.all().order_by("-created_at"), many=True).data}
        )
    except Exception as err:
        logger.error(f"admin_add_update_post() - Unable to create update post error : {err}")
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@staff_only
def admin_edit_update_post(request):
    """
    Edit the Update post.
    :param request: Django Rest Framework's Request object.
    """

    try:
        update_post_id = request.data['id']
        title: str = request.data['title']
        description: str = request.data['description']
        date = request.data['date']

    except KeyError as k:
        logger.error(f"admin_edit_update_post() - Missing key {k}")
        return JsonResponseBadRequest()

    if UpdateSection.objects.filter(id=update_post_id).exists():
        update_section_obj = UpdateSection.objects.get(id=update_post_id)

        formatted_datetime = datetime.fromisoformat(date)
        if timezone.is_naive(formatted_datetime):
            formatted_datetime = timezone.make_aware(formatted_datetime)

        update_section_obj.title = title
        update_section_obj.description = description
        update_section_obj.created_at = formatted_datetime
        update_section_obj.save()

        return JsonResponseSuccess(
            {'data': UpdateSectionSerializer(UpdateSection.objects.all().order_by("-created_at"), many=True).data}
        )

    return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@staff_only
def admin_delete_udpate_post(request):
    """
    Delete Changelogs.
    :param request: Django Rest Framework's Request object.
    """
    try:
        update_post_ids = request.data['updatePostIds']
    except KeyError as k:
        logger.error(f"admin_delete_udpate_post() - Missing key {k}")
        return JsonResponseBadRequest()

    if UpdateSection.objects.filter(id__in=update_post_ids).exists():
        UpdateSection.objects.filter(id__in=update_post_ids).delete()

        return JsonResponseSuccess(
            {'data': UpdateSectionSerializer(UpdateSection.objects.all().order_by("-created_at"), many=True).data, }
        )

    return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@staff_only
def admin_get_update_post(request):
    """
    Get admin Update Posts.

    :param request: Django Rest Framework's Request object.
    """
    return JsonResponseSuccess({
        'data': UpdateSectionSerializer(UpdateSection.objects.all().order_by("-created_at"), many=True).data
    }
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_user_verify_email(request):
    """
    API to Admin verify user.
    """
    try:
        user_id: int = request.data.get("id")
        verify_user: bool = request.data.get("verify_email")
    except KeyError as k:
        logger.critical(f"admin_user_verify_email() - Missing Key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_user_verify_email() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest(data={
            "message": "Could not find resource on server"
        })

    user.email_verified = verify_user
    user.save()

    return JsonResponseSuccess()


# @api_view(["GET"])
# @permission_classes([IsAuthenticated])
# @staff_only
# def admin_get_all_campaign(request):
#     reset_queries()
#     start = time.time()
#
#     latest_activity = (
#         CampaignActivity.objects
#         .annotate(
#             rn=Window(
#                 expression=RowNumber(),
#                 partition_by=[F("campaign_id"), F("event_from")],
#                 order_by=F("event_date").desc(),
#             )
#         )
#         .filter(rn=1)
#         .values("campaign")
#         .annotate(unsub_count=Count("event_from", filter=Q(event_type="unsubscribe"), distinct=True))
#         .values("campaign", "unsub_count")
#     )
#
#     unsub_count_subquery = Subquery(
#         latest_activity.filter(campaign=OuterRef("pk")).values("unsub_count")[:1]
#     )
#
#     campaigns = (
#         Campaign.objects
#         .select_related("workspace__user")
#         .annotate(
#             total_contacts=Count("campaigncontact", distinct=True),
#             user_id=F("workspace__user__id"),
#             total_sent=Count("campaignschedule", filter=Q(campaignschedule__sent_on__isnull=False), distinct=True),
#             total_bounced=F("bounces"),
#             total_unsubscribe=unsub_count_subquery,
#             positive_replies=Count("campaignschedule", filter=Q(campaignschedule__reply_classification="positive"), distinct=True),
#             neutral_replies=Count("campaignschedule", filter=Q(campaignschedule__reply_classification="neutral"), distinct=True),
#             negative_replies=Count("campaignschedule", filter=Q(campaignschedule__reply_classification="negative"), distinct=True),
#         )
#     )
#
#     serializer = AdminCampaignSerializer(campaigns, many=True)
#     end = time.time()
#
#     logger.info(len(serializer.data))
#
#     logger.info(f"Execution Time: {end - start:.2f}s")
#     logger.info(f"connection.queries {len(connection.queries)}")
#     for q in connection.queries:
#         logger.info(f"SQL: {q['sql']} | Time: {q['time']}")
#
#     return JsonResponseSuccess(data={"data": serializer.data})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@staff_only
def admin_update_warmup_date(request):
    """
    Admin API to update warmup datetime
    """
    try:
        managed_subdomain_id = request.data['id']
        warmup_datetime = request.data['warmup_datetime']
    except KeyError as k:
        logger.critical(f"admin_update_warmup_date() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    warmup_datetime = dateparse.parse_datetime(warmup_datetime)
    if not warmup_datetime:
        return JsonResponseBadRequest(data={
            "message": "Invalid datetime format"
        })

    managed_subdomain = ManagedSubdomain.objects.get(id=managed_subdomain_id)
    managed_subdomain.domain_usable_from = warmup_datetime
    managed_subdomain.save()

    return JsonResponseSuccess()
