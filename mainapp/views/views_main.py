import html
import json
import logging
import math
import os
import random
import re
import time
import uuid
from datetime import datetime
from typing import List, Dict
from zoneinfo import ZoneInfo

import boto3
import botocore.exceptions
import pytz
import redis
import requests
import stripe.checkout
from cryptography.fernet import Fernet
from dateutil.relativedelta import relativedelta
from django.contrib.auth.hashers import check_password, make_password
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.db import IntegrityError
from django.db.models import Count, Sum, OuterRef, Subquery, Window, Q, F, OuterRef, Max
from django.db.models.functions import RowNumber
from django.utils import timezone
from pydantic import TypeAdapter
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from tutorial.settings import DEBUG

from ColdEmailerBackend.settings import aws_eventbridge, aws_s3_client, DAILY_EMAIL_LIMIT_PERCENT_INCR_MIN, \
    DAILY_EMAIL_LIMIT_PERCENT_INCR_MAX, MAX_DAILY_EMAIL_LIMIT_CAP, CONTACT_LIST_MAX_ROWS, CONTACT_LIST_MAX_COLS
from mainapp.aws import get_hosted_zone_nameservers
from mainapp.campaign import CampaignManager, CampaignNotFound, CampaignSaveFailed, \
    CampaignContactNotFound
from mainapp.email_messages import reset_password_email_message, new_signup_email_message, \
    signup_onboarding_email_message, campaign_completed_email_message, \
    campaign_paused_email_message, campaign_resumed_email_message, credits_exhausted_email_message, \
    approval_request_email_message, account_email_verification_email_body, new_integration_request_email_message
from mainapp.models import User, ManagedSubdomain, Campaign, CampaignContact, CampaignSchedule, BadEmail, Workspace, \
    CampaignActivity, EmailID, SubscriptionPlan, CampaignEmailMessage, BlacklistCheckStatus, UnsubscribedEmail, \
    UpdateSection, ImportedContactList, EmailLinkClickEvent
from mainapp.pydantic_models import SaveCampaignMessageSchema, GooglePostmasterDomainSchema
from mainapp.responses import JsonResponseSuccess, JsonResponseBadRequest, JsonResponseForbidden, JsonResponseNotFound, \
    JsonResponseServerError, JsonResponseRedirect
from mainapp.route53 import add_route53_record, delete_route53_records
from mainapp.serializers import CampaignSerializer, CampaignActivitySerializer, WorkspaceSerializer, \
    CampaignExportContactSerializer, CreditHistorySerializer, UpdateSectionSerializer
from mainapp.tasks import camapign_reply_handler_task, duplicate_campaign_task, delete_workspace_task, \
    create_campaign_task, pause_campaign_task, start_campaign_task, cancel_campaign_task, resume_campaign_task, \
    send_email_task, contact_list_setup_task
from mainapp.utils import send_websocket_event, get_email_body, send_email, are_campaign_emails_valid, get_ip_country, \
    generate_verification_token, decrypt_verification_token

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


@api_view(["GET"])
@permission_classes([AllowAny])
def test_api_get(_request):
    """
    Use for testing and debugging purposes.
    """
    return JsonResponseSuccess(data={
        "message": "OK"
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def test_api_post(_request):
    """
    Use for testing and debugging purposes.
    """
    return JsonResponseSuccess()


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def login(request):
    if request.method == "GET":
        return JsonResponseSuccess()

    else:
        try:
            email: str = request.data['email']
            password: str = request.data['password']
        except KeyError as k:
            logger.critical(f"Login Error: Key {k} is missing.")
            return JsonResponseBadRequest(data={'message': f"Login Error: Key {k} is missing."})

        # clean & sanitize input
        email = html.escape(email.strip().lower())

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            # Wrong email
            logger.debug(f"User {email} does not exist.")
            return JsonResponseForbidden(data={"message": f"Provided email and/or password is incorrect."})

        if check_password(password, user.password):
            # Generate jwt tokens and send them back in response
            tokens = RefreshToken.for_user(user)
            user.last_login = datetime.now(tz=ZoneInfo('UTC'))
            user.save()

            # noinspection PyUnresolvedReferences
            return JsonResponseSuccess(data={
                'refresh_token': str(tokens),
                'access_token': str(tokens.access_token),
            })

        else:
            # Wrong password
            logger.debug(f"Incorrect password for {email}")
            return JsonResponseForbidden(data={"message": f"Provided email and/or password is incorrect."})


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def signup(request):
    if request.method == "GET":
        return JsonResponseSuccess()

    else:
        try:
            username: str = request.data['username']
            email: str = request.data['email']
            password: str = request.data['password']
            price_id: str | None = request.data['price_id']
            tz = request.data.get('timezone', 'UTC')
            affiliate_id = request.data.get('affiliate_id', None)
        except KeyError as k:
            logger.critical(f"Signup Error: Key {k} is missing.")
            return JsonResponseBadRequest(data={"message": f"Signup Error: Key {k} is missing."})

        # Clean & sanitize inputs (except password).
        username = html.escape(username.strip())
        email = html.escape(email.strip().lower())

        # Do not proceed if email is bad.
        try:
            validate_email(email)
        except ValidationError:
            logger.debug(f"Signup Error: email {email} validation failed during frontend signup")
            return JsonResponseBadRequest(data={"message": f"Please enter a valid email address."})

        # Check password conditions
        if len(password) < 6:
            return JsonResponseBadRequest(data={"message": f"Password must be at least 6 characters long."})

        # Fetch IP location.
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')

        try:
            country: str = get_ip_country(ip)
        except Exception as err:
            logger.critical(f"{err}")
            country = "United States"

        # Create user and default workspace.
        try:
            user: User = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                country=country,
            )
            user.save()

            try:
                free_plan = SubscriptionPlan.objects.get(is_free_plan=True)
            except SubscriptionPlan.DoesNotExist:
                logger.critical(
                    "create_workspace() - Failed to fetch free plan. Make sure 'is_free_plan' field is set.")
                return JsonResponseServerError(data={
                    "message": "Server Error. Please try again in some time."
                })

            # Create new workspace.
            default_workspace = Workspace.objects.create(
                user=user,
                credits_remaining=free_plan.monthly_email_sending_quota,
                subscription_plan=free_plan,
                billing_period="monthly",
                next_renewal_date=timezone.now() + relativedelta(months=+1),
                time_zone=tz
            )

            user.active_workspace = default_workspace
            user.save()

        except IntegrityError:
            logger.debug(f"Signup Error: Account with email id {email} already exists")
            return JsonResponseBadRequest({"message": "Account with this email id already exists. "
                                                      "Please try logging in with the correct credentials."})

        # Generate jwt tokens and send them back in the response.
        tokens = RefreshToken.for_user(user)

        # If price id is present, we need to create a checkout session.
        if price_id:
            # Set up subscription line items.
            if country.lower() == "india":
                currency: str = "INR"
                billing_address_collection: str = "required"
                line_items: List[Dict] = [{
                    "price": price_id,
                    "quantity": 1,
                    "tax_rates": [os.environ["CE_STRIPE_INR_TAX_RATE_ID"]]
                }]
            else:
                currency: str = "USD"
                billing_address_collection: str = "auto"
                line_items: List[Dict] = [{"price": price_id, "quantity": 1}]

            billing_period = "monthly" if SubscriptionPlan.objects.filter(
                monthly_price_id=price_id).count() > 0 else "annual"

            # Discount
            if user.is_new_user_discount_eligible:
                if billing_period == "monthly":
                    discounts = [{"coupon": os.environ["CE_STRIPE_NEW_USER_MONTH_COUPON_ID"]}]
                else:
                    discounts = [{"coupon": os.environ["CE_STRIPE_NEW_USER_YEAR_COUPON_ID"]}]
            else:
                discounts = []

            # Subscription data.
            subscription_data: Dict = {
                "metadata": {
                    "workspace_id": user.active_workspace.id  # We need this to identify workspace in webhooks.
                },
            }
            if user.is_trial_period_eligible:
                subscription_data["trial_period_days"] = 7

                # stripe_checkout_args = {
                #     "mode": "subscription",
                #     "currency": currency,
                #     "billing_address_collection": billing_address_collection,
                #     "line_items": line_items,
                #     "ui_mode": "hosted",
                #     "success_url": os.environ["CE_STRIPE_SUCCESS_URL"],
                #     "cancel_url": cancel_url,
                #     "discounts": discounts,
                #     "subscription_data": subscription_data,
                # }
                #
                # if user.stripe_customer_id:
                #     stripe_checkout_args["customer"] = user.stripe_customer_id
                #
                # if affiliate_id:
                #     stripe_checkout_args["client_reference_id"] = affiliate_id

            logger.info(f"Affiliate ID -> {affiliate_id}")

            session = stripe.checkout.Session.create(
                customer_email=user.email,
                mode="subscription",
                currency=currency,
                billing_address_collection=billing_address_collection,
                line_items=line_items,
                ui_mode="hosted",
                client_reference_id=affiliate_id,
                success_url=os.environ["CE_STRIPE_SUCCESS_URL"],
                cancel_url=os.environ["CE_APP_HOST_URL"] + "/onboarding/plan-selection",
                discounts=discounts,
                subscription_data=subscription_data,
            )

            token = generate_verification_token(user.email)
            verify_url = f"{os.environ['CE_APP_HOST_URL']}/auth/verify-account-email/{token}"

            # Send email to ourselves.
            send_email_task.delay(
                to=os.environ["CE_NEW_SIGNUP_ALERT_EMAILS"].split(","),
                sender="<EMAIL>",
                sender_name="Deliveryman",
                subject="New signup on Deliveryman.ai",
                body_html=new_signup_email_message(username=user.username, email=email)
            )

            send_email_task.delay(
                to=user.email,
                sender="<EMAIL>",
                sender_name="Junaid Ansari",
                subject=f"Verify your email to unlock DeliverymanAI",
                body_html=account_email_verification_email_body(username=user.username, verify_url=verify_url,
                                                                current_year=str(datetime.now().year))
            )

            # noinspection PyUnresolvedReferences
            return JsonResponseSuccess(data={
                'refresh_token': str(tokens),
                'access_token': str(tokens.access_token),
                'session_url': session["url"],
            })

        else:
            # # Send email to ourselves.
            # send_email_task.delay(
            #     to=os.environ["CE_NEW_SIGNUP_ALERT_EMAILS"].split(","),
            #     sender="<EMAIL>",
            #     sender_name="Deliveryman",
            #     subject="New signup on Deliveryman.ai",
            #     body_html=new_signup_email_message(username=username, email=email)
            # )

            # # Send email to user.
            # send_email_task.delay(
            #     to=user.email,
            #     sender="<EMAIL>",
            #     sender_name="Junaid Ansari",
            #     subject=f"Welcome to DeliverymanAI, {user.username}",
            #     body_html=signup_onboarding_email_message(
            #         username=user.username,
            #         dashboard_url=os.environ["CE_APP_HOST_URL"] + "/dashboard",
            #         current_year=str(datetime.now().year),
            #     )
            # )
            token = generate_verification_token(user.email)
            verify_url = f"{os.environ['CE_APP_HOST_URL']}/auth/verify-account-email/{token}"

            # Send email to ourselves.
            send_email_task.delay(
                to=os.environ["CE_NEW_SIGNUP_ALERT_EMAILS"].split(","),
                sender="<EMAIL>",
                sender_name="Deliveryman",
                subject="New signup on Deliveryman.ai",
                body_html=new_signup_email_message(username=user.username, email=email)
            )

            send_email_task.delay(
                to=user.email,
                sender="<EMAIL>",
                sender_name="Junaid Ansari",
                subject=f"Verify your email to unlock DeliverymanAI",
                body_html=account_email_verification_email_body(username=user.username, verify_url=verify_url,
                                                                current_year=str(datetime.now().year))
            )

            # noinspection PyUnresolvedReferences
            return JsonResponseSuccess(data={
                'refresh_token': str(tokens),
                'access_token': str(tokens.access_token),
                'session_url': None,
            })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def signup_plan_selection(request):
    """

    :param request:
    :return:
    """
    user: User = request.user
    if user.signup_plan_selection_done:
        return JsonResponseRedirect("/dashboard")

    free_plan = SubscriptionPlan.objects.get(is_free_plan=True)

    plans: List[Dict] = [{
        "id": free_plan.id,
        "name": free_plan.plan_name,
        "monthly_amount": free_plan.monthly_amount,
        "annual_amount": free_plan.annual_amount,
        "popular": free_plan.popular,
        "monthly_feature_list": free_plan.monthly_feature_list,
        "annual_feature_list": free_plan.annual_feature_list,
    }]
    for plan in SubscriptionPlan.objects.filter(hidden=False):
        plans.append({
            "id": plan.id,
            "name": plan.plan_name,
            "monthly_amount": plan.monthly_amount,
            "annual_amount": plan.annual_amount,
            "popular": plan.popular,
            "monthly_feature_list": plan.monthly_feature_list,
            "annual_feature_list": plan.annual_feature_list,
        })

    return JsonResponseSuccess(data={
        "plans": plans,
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def send_reset_password_link(request):
    """
    API for mailing reset password link to given email address if an account exists.
    """
    try:
        try:
            email_id: str = html.escape(request.data['email_id'].strip().lower())
        except KeyError as k:
            logger.critical(f"send_reset_password_link() - Missing key {k}", exc_info=True)
            return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

        try:
            user: User = User.objects.get(email=email_id)
        except User.DoesNotExist:
            return JsonResponseSuccess()

        # Generate the token and set the date.
        f = Fernet(os.environ["CE_PASSWORD_RESET_FERNET_KEY"])
        token = f.encrypt(f"{user.email}-{timezone.now().timestamp()}".encode()).decode("utf-8")
        user.reset_password_token = token
        user.reset_password_request_date = timezone.now()
        user.save()
        reset_password_link = f"{os.environ['CE_APP_HOST_URL']}/auth/change-password?token={token}"

        # Send the forgot password email.
        message: str = reset_password_email_message(user.username, reset_password_link)
        send_email(
            to=email_id,
            sender="<EMAIL>",
            sender_name="Deliveryman.ai",
            subject="Your Password Reset Request on Deliveryman.ai",
            body_html=message,
        )

        return JsonResponseSuccess()

    except Exception as err:
        logger.critical(err, exc_info=True)
        return JsonResponseBadRequest(data={"message": "Server Error. Please try again later or contact us if "
                                                       "the issue persists."})


@api_view(["POST"])
@permission_classes([AllowAny])
def change_password(request):
    """
    API for changing user account password.
    """
    try:
        token: str = request.data['token']
        new_password: str = html.escape(request.data['new_password'].strip())
    except KeyError as k:
        logger.critical(f"send_reset_password_link() - Missing key {k}", exc_info=True)
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        user: User = User.objects.get(reset_password_token=token)
    except User.DoesNotExist:
        return JsonResponseNotFound(data={"message": "Invalid or expired change password request."})

    # Check password reset token.
    if user.reset_password_token != token:
        return JsonResponseBadRequest(data={"message": "Invalid or expired change password request."})

    # Check password reset expiry.
    if user.reset_password_request_date and (
            (timezone.now() - user.reset_password_request_date).total_seconds() > 86400):
        return JsonResponseBadRequest(data={"message": "Invalid or expired change password request."})

    # Check new password length.
    if len(new_password) < 6:
        return JsonResponseBadRequest(data={
            "message": "Password must be at least 6 characters long."
        })

    # Update password.
    user.password = make_password(new_password)
    user.reset_password_token = None
    user.reset_password_request_date = None
    user.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([AllowAny])
def logout(request):
    """
    Logs out user by blacklisting their refresh token.
    """
    refresh_token: str = request.data['refresh']
    # noinspection PyTypeChecker
    token = RefreshToken(refresh_token)
    token.blacklist()
    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def base_page_data(request):
    """
    API for logged in Base page data.
    """
    user: User = request.user

    return JsonResponseSuccess(data={
        "free_plan_user": user.active_workspace.subscription_plan.is_free_plan,
        "monthly_emails_remaining": user.active_workspace.credits_remaining,
        "user_verified": user.email_verified,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def workspaces(request):
    """
    API that returns all workspaces for this account
    """
    user: User = request.user

    return JsonResponseSuccess(data={
        "workspaces": WorkspaceSerializer(user.workspace_set.all(), many=True).data,
        "active_workspace": WorkspaceSerializer(user.active_workspace).data,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_workspace(request):
    """
    API that creates a new workspace and makes it current active workspace.
    """
    user: User = request.user

    # Do not allow if user has not purchased a plan yet.
    if not user.has_purchased_plan:
        return JsonResponseBadRequest(data={
            "message": "You can only create additional workspaces by "
                       "purchasing at least one subscription on this account."
        })

    try:
        workspace_name: str = html.escape(request.data["workspace_name"].strip())
        tz = request.data.get('timezone', 'UTC')
    except KeyError as k:
        logger.critical(f"create_workspace() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Run checks.
    if not workspace_name:
        return JsonResponseBadRequest(data={
            "message": "Campaign name is required. Please enter a valid name less than 50 characters."
        })

    if len(workspace_name) > 50:
        return JsonResponseBadRequest(data={
            "message": "Please keep workspace name below 50 characters."
        })
    try:
        free_plan = SubscriptionPlan.objects.get(is_free_plan=True)
    except SubscriptionPlan.DoesNotExist:
        logger.critical("create_workspace() - Failed to fetch free plan. Make sure 'is_free_plan' field is set.")
        return JsonResponseServerError(data={
            "message": "Server Error. Please try again in some time."
        })

    # Create new workspace.
    new_workspace = Workspace.objects.create(
        user=user,
        name=workspace_name,
        credits_remaining=free_plan.monthly_email_sending_quota,
        subscription_plan=free_plan,
        billing_period="monthly",
        next_renewal_date=timezone.now() + relativedelta(months=+1),
        time_zone=tz
    )

    user.active_workspace = new_workspace
    user.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def switch_workspace(request):
    """
    API to switch active workspace.
    """
    user: User = request.user

    try:
        workspace_id: int = int(html.escape(request.data["workspace_id"].strip()))
    except KeyError as k:
        logger.critical(f"switch_workspace() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    except Exception as err:
        logger.critical(f"switch_workspace() - Exception: {err}")
        return JsonResponseServerError(data={
            "message": "Server Error. Please try again later or contact support if issue persists."
        })

    try:
        workspace: Workspace = user.workspace_set.get(id=workspace_id)
    except Workspace.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "No such workspace was found."
        })

    user.active_workspace = workspace
    user.save()

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def dashboard(request):
    """
    Dashboard page API.
    """
    user: User = request.user

    total_emails = 0
    for msub in user.active_workspace.managedsubdomain_set.all():
        for esub in msub.emailsubdomain_set.all():
            total_emails += esub.emailid_set.count()

    return JsonResponseSuccess(data={
        "total_campaigns": user.active_workspace.campaign_set.count(),
        "total_contacts": sum(
            [campaign.campaigncontact_set.count() for campaign in user.active_workspace.campaign_set.all()]
        ),
        "total_managed_subdomains": user.active_workspace.managedsubdomain_set.count(),
        "total_emails": total_emails,
        "total_emails_sent": user.active_workspace.total_emails_sent,
        "total_replies_received": user.active_workspace.total_replies_received,

        "monthly_emails_remaining": user.active_workspace.credits_remaining,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_settings(request):
    """
    API that returns data for account settings page.
    """
    user: User = request.user

    return JsonResponseSuccess(data={
        "username": user.username,
        "user_email": user.email,
        "workspace_name": user.active_workspace.name,
        "workspace_id": user.active_workspace.id,
        "blacklist_email": user.active_workspace.blacklist_email,
        "user_verified": user.email_verified,
        "time_zone": user.active_workspace.time_zone
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def save_workspace_settings(request):
    """
    API that saves data for account workspace settings page.
    """
    user: User = request.user

    try:
        workspace_name: str = html.escape(request.data["workspace_name"].strip())
        blacklist_email: bool = request.data["blacklist_email"]
        time_zone: str = request.data["time_zone"]
    except KeyError as k:
        logger.critical(f"save_workspace_settings() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # ----------- Perform Checks -----------
    if len(workspace_name) == 0:
        return JsonResponseBadRequest(data={
            "message": "Workspace name cannot be empty. Please enter a valid name less than 50 characters."
        })

    if len(workspace_name) > 50:
        return JsonResponseBadRequest(data={
            "message": "Please keep workspace name below 50 characters."
        })

    user.active_workspace.blacklist_email = blacklist_email
    user.active_workspace.name = workspace_name
    user.active_workspace.time_zone = time_zone
    user.active_workspace.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def delete_workspace(request):
    """
    API to delete given workspace. Switches user to a different workspace. If there are no other workspace available,
    rejects the deletion request.
    """
    user: User = request.user

    try:
        workspace_id: int = request.data["workspace_id"]
    except KeyError as k:
        logger.critical(f"delete_workspace() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # Check if workspace exists in user account.
    try:
        workspace: Workspace = user.workspace_set.get(id=workspace_id)
    except Workspace.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "No such workspace was found."
        })

    # Fetch other available workspaces in this account. If none are aviailable, do not proceed with the deletion.
    other_workspaces = user.workspace_set.exclude(id=workspace_id)

    if other_workspaces.count() == 0:
        return JsonResponseBadRequest(data={
            "message": "You cannot delete your only workspace. Please create and switch to another workspace before "
                       "deleting this one."
        })

    # Unlink from account.
    workspace.user = None
    workspace.save()

    # Switch user to different workspace.
    user.active_workspace = other_workspaces[0]
    user.save()

    # Start the deletion task.
    delete_workspace_task.delay(workspace_id)

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def connect_exising_domain(request):
    """
    Connect Existing Domain page API.
    """
    user: User = request.user

    current_domain_count = user.active_workspace.managedsubdomain_set.filter(setup_complete=True).count()
    total_domain_connection_allowed = user.active_workspace.subscription_plan.total_domain_connections_allowed
    logger.debug(f"current_domain_count: {current_domain_count}")
    logger.debug(f"total_domain_connection_allowed: {total_domain_connection_allowed}")
    if current_domain_count >= total_domain_connection_allowed:
        return JsonResponseForbidden(data={
            "message": "max domain limit reached."
        })

    # If subdomain query params is provided, we send data for that domain so setup can resume.
    # Otheriwse it is assumed this is a new setup.
    subdomain: str = request.query_params.get('subdomain', None)

    if subdomain:
        try:
            managed_subdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
        except ManagedSubdomain.DoesNotExist:
            logger.error(f"connect_exising_domain() - Could not find subdomain {subdomain} for account {user.email}")
            return JsonResponseNotFound(data={
                "message": f"Could not find resource on server."
            })

        nameservers: List[str] = get_hosted_zone_nameservers(managed_subdomain.hosted_zone_id)

        return JsonResponseSuccess(data={
            "setup_data": {
                "setup_complete": managed_subdomain.setup_complete,
                "current_stage": managed_subdomain.current_setup_stage,
                "subdomain": managed_subdomain.subdomain,
                "nameservers": nameservers,
                "naming_strategy": managed_subdomain.naming_strategy,
                "custom_name": managed_subdomain.custom_name,
                "contacts_count": managed_subdomain.contacts_count,
            }
        })

    else:
        return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def email_accounts(request):
    """
    API for email accounts page.
    """
    user: User = request.user

    managed_subdomains_data: List[Dict] = []

    latest_blacklist = (
        BlacklistCheckStatus.objects.filter(subdomain=OuterRef("pk"))
        .order_by("-checked_on")
    )

    subdomains = (
        user.active_workspace.managedsubdomain_set.all()
        .prefetch_related("emailsubdomain_set")
        .annotate(
            latest_blacklist_sources=Subquery(
                latest_blacklist.values("blacklisted_sources")[:1]
            ),
            email_subdomain_count=Count("emailsubdomain", distinct=True),
            total_emails=Count("emailsubdomain__emailid", distinct=True),
        )
        .order_by("-created_on")
    )

    for msub in subdomains:
        blacklist_sources = msub.latest_blacklist_sources

        if blacklist_sources is None:
            blacklist_status = "Incomplete"
        elif not blacklist_sources:
            blacklist_status = "Healthy"
        elif any("Unknown error" in s for s in blacklist_sources):
            blacklist_status = "Unknown Error"
        else:
            blacklist_status = ", ".join(blacklist_sources)

        # postmaster_data = get_postmaster_data(user, msub.subdomain)
        managed_subdomains_data.append({
            "id": msub.id,
            "managed_subdomain": msub.subdomain,
            "email_subdomain_count": msub.email_subdomain_count,
            "total_emails": msub.total_emails,
            "max_contacts": msub.contacts_count,
            "setup_complete": msub.setup_complete,
            "created_on_ts": msub.created_on.timestamp() * 1000,
            "status": msub.status,
            "seconds_remaining": max(int((msub.domain_usable_from - timezone.now()).total_seconds()), 0),
            "black_list_status": blacklist_status,

            # # Postmaster data.
            # "postmaster_integration_active": postmaster_data.integration_active,
            # "postmaster_http_error": postmaster_data.httpError,
            # "domain_reputation": postmaster_data.domain_reputation,
            # "spam_ratio": postmaster_data.spam_ratio,

            # Domain redirection.
            "redirect_domain": msub.redirect_subdomains_to,
        })

    return JsonResponseSuccess(data={
        "managed_subdomains": managed_subdomains_data,
        "timezone": user.active_workspace.time_zone,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def email_accounts__emails(request):
    """
    API for Emails page data.
    """
    user: User = request.user

    try:
        domain: str = request.query_params["domain"]
    except KeyError as k:
        logger.critical(f"email_accounts__emails() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        msub: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=domain)
    except ManagedSubdomain.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find resource on server."
        })

    emails_data: List[Dict] = []
    for email in EmailID.objects.prefetch_related("email_subdomain").filter(email_subdomain__managed_subdomain=msub):
        emails_data.append({
            "address": email.email_address,
            "name": email.username,
            "subdomain": email.email_subdomain.subdomain,
        })

    return JsonResponseSuccess(data={
        "emails": emails_data
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def campaigns(request):
    """
    API for campaigns page.
    """
    user: User = request.user

    return JsonResponseSuccess(data={
        "campaigns": CampaignSerializer(
            user.active_workspace.campaign_set.filter(archived=False).order_by("-created_on"), many=True
        ).data,
        "archived_campaigns": CampaignSerializer(
            user.active_workspace.campaign_set.filter(archived=True).order_by("-created_on"), many=True
        ).data,
        "user_id": user.id,
        "timezone": user.active_workspace.time_zone,
    })


@api_view(["POST", "GET"])
@permission_classes([IsAuthenticated])
def create_campaign(request):
    """
    API to create a new campaign.
    """
    user: User = request.user

    if request.method == "GET":
        # Fetch connected domains that are not in initial warmup state and not in any running/created campaigns.
        msubs = []
        domains = (user.active_workspace.managedsubdomain_set
                   .filter(setup_complete=True)
                   .exclude(campaign__status__in=["creating", "created", "scheduled", "running", "paused"]))
        for msub in domains:
            msubs.append({
                "id": str(msub.id),
                "domain": msub.subdomain,
                "checked": False,
            })

        # Fetch all active contact lists.
        active_contact_lists = []
        for cl in user.active_workspace.importedcontactlist_set.prefetch_related("contact_set").filter(status="active"):
            column_count: int = len(cl.contact_set.all()[0].get_attribute_names()) + 1  # +1 for email column.
            active_contact_lists.append({
                "uid": cl.uid,
                "name": cl.name,
                "created_on": int(cl.created_on.timestamp() * 1000),
                "total_contacts": cl.contact_set.count(),
                "total_columns": column_count,
            })

        return JsonResponseSuccess(data={
            "campaigns_blocked": user.is_campaign_blocked,
            "managed_subdomains": msubs,
            "contact_lists": active_contact_lists,
        })

    else:
        try:
            campaign_name: str = html.escape(request.data["name"].strip())
            selected_contact_list_uids: List[str] = request.data["selected_contact_list_uids"]
            emails_per_day: int = int(request.data["emails_per_day"]) if request.data["emails_per_day"] != "" else 0
            reply_to_address: str = html.escape(request.data["reply_to_address"].strip())
            selected_domain_ids: List[int] = request.data["selected_domain_ids"]
            skip_days: List[str] = request.data["skip_days"]
        except KeyError as k:
            logger.error(f"create_campaign() - Missing key {k}")
            return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

        # Check if user account is blocked.
        if user.is_campaign_blocked:
            return JsonResponseForbidden(data={
                "message": "Account blocked from creating new cmapaigns."
            })

        # check if at least one managed domain has been selected.
        if len(selected_domain_ids) == 0:
            return JsonResponseBadRequest(data={
                "message": "Please select at least one domain for sending emails in this campaign."
            })

        # check the number of skipped days.
        if len(skip_days) >= 7:
            return JsonResponseBadRequest(data={
                "message": "You cannot skip all days in a week."
            })

        # Check for emails per day validity.
        if emails_per_day < 0:
            return JsonResponseBadRequest(data={
                "message": "Please enter a valid positive integer value for Emails Per Day."
            })

        # Check reply to address.
        if not reply_to_address:
            reply_to_address = user.email
        else:
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, reply_to_address):
                return JsonResponseBadRequest(data={
                    "message": "Please enter a valid reply-to email address or leave it empty."
                })

        # Create the campaign.
        logger.debug("Creating campaigns...")
        camp_uid: str = f"campaign_{uuid.uuid4().hex}"
        new_campaign = Campaign.objects.create(
            workspace=user.active_workspace,
            uid=camp_uid,
            name=campaign_name,
            reply_to_address=reply_to_address,
            emails_per_day=emails_per_day,
            skip_days=skip_days,
        )

        logger.debug("Starting create campaign celery task...")
        create_campaign_task.delay(
            user_id=user.id,
            campaign_id=new_campaign.id,
            selected_contact_list_uid=selected_contact_list_uids[0],
            selected_domain_ids=selected_domain_ids,
        )

        user.has_sent_campaign_creation_reminder_email = True
        user.save()

        return JsonResponseSuccess(data={
            "uid": camp_uid
        })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def create_campaign__fetch_contact_lists(request):
    """
    API to fetch only contact list data. Used for table refresh button.
    """
    user: User = request.user

    # Fetch all active contact lists.
    active_contact_lists = []
    for cl in user.active_workspace.importedcontactlist_set.prefetch_related("contact_set").filter(status="active"):
        column_count: int = len(cl.contact_set.all()[0].get_attribute_names()) + 1  # +1 for email column.
        active_contact_lists.append({
            "uid": cl.uid,
            "name": cl.name,
            "created_on": int(cl.created_on.timestamp() * 1000),
            "total_contacts": cl.contact_set.count(),
            "total_columns": column_count,
        })

    return JsonResponseSuccess(data={
        "contact_lists": active_contact_lists,
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def duplicate_campaign(request):
    """
    API for duplicating a finished campaign.
    """
    user: User = request.user

    try:
        target_campaign_uid: str = request.data["target_campaign_uid"]
        campaign_name: str = html.escape(request.data["campaign_name"].strip())
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign = user.active_workspace.campaign_set.get(uid=target_campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Resource not found on server."
        })

    if campaign.status == "complete" or campaign.status == "cancelled":
        new_campaign_uid: str = f"campaign_{uuid.uuid4().hex}"

        try:
            Campaign.objects.create(
                workspace=user.active_workspace,
                uid=new_campaign_uid,
                name=campaign_name,
                reply_to_address=user.email,
            )

            # Start the duplication task.
            duplicate_campaign_task.delay(target_campaign_uid, new_campaign_uid)

        except Exception as err:
            logger.critical(err, exc_info=True)
            return JsonResponseServerError(data={
                "message": "Server error. Please try again later and contact us if the issue persists."
            })

        return JsonResponseSuccess(data={
            "uid": new_campaign_uid
        })

    else:
        return JsonResponseBadRequest({
            "message": "This campaign has not finished yet."
        })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def setup_campaign_emails(request):
    """
    API for campaign email messages setup page.

    :param request:
    :return:
    """
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"setup_campaign_emails() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"setup_campaign_emails() - Campaign {campaign_uid} does not exist in account {user.email}")
        return JsonResponseNotFound(data={
            "message": "Could not find resource on server"
        })

    email_variables_data: List[Dict] = list(campaign.campaigncontact_set.values_list("attributes", flat=True))

    return JsonResponseSuccess(data={
        "variables": list(email_variables_data[0].keys()),
        "email_messages": [{
            "uid": email.uid,
            "subject": email.subject,
            "body": email.body,
            "next_message_days": email.next_message_days,
            "score": email.score,
            "verdict": email.verdict,
            "suggestions": email.suggestions,
            "approval_flag": email.is_flagged_for_approval,
            "label": email.label,
            "content_type": email.email_content_type,
        } for email in campaign.campaignemailmessage_set.all()]
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def generate_campaign_message_uid(_request):
    """
    Generates and returns a new campaign uid string.
    """
    return JsonResponseSuccess(data={
        "uid": f"campaign_message_{uuid.uuid4().hex}"
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def save_campaign_setup_changes(request):
    """
    API for saving campaign email messages.
    """
    user: User = request.user
    ta = TypeAdapter(List[SaveCampaignMessageSchema])

    try:
        campaign_uid: str = request.data["campaign_uid"]
        email_data: List[SaveCampaignMessageSchema] = ta.validate_python(request.data["email_data"], strict=True)
    except KeyError as k:
        logger.critical(f"save_campaign_setup_changes() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})
    except ValidationError as err:
        logger.critical(f"Pydantic Validation Error: {err}")
        return JsonResponseBadRequest(data={"message": "Internal Server Error. Please try again after some time."})

    # Create an instance of campaign manager.
    try:
        cm = CampaignManager(campaign_uid)
    except CampaignNotFound:
        logger.error(f"add_campaign_email_message() - Campaign with UID {campaign_uid} does not exist in "
                     f"account {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    try:
        logger.debug("Saving changes...")
        cm.save_email_messages(email_data)
    except CampaignSaveFailed as err:
        return JsonResponseBadRequest(data={"message": f"{err}"})

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def request_manual_approval(request):
    """
    API for requesting a manual approval on blocked email contents.
    """
    user = request.user

    try:
        message_uid: str = request.data["message_uid"]
    except KeyError as k:
        logger.critical(f"request_manual_approval() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        message = CampaignEmailMessage.objects.get(uid=message_uid)
    except CampaignEmailMessage.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find email message on server."
        })

    message.is_flagged_for_approval = True
    message.save()

    send_email_task.delay(
        to=os.environ["CE_APPROVAL_REQUEST_EMAILS"].split(","),
        sender="<EMAIL>",
        sender_name="Deliveryman",
        subject=f"Approval Request for {user.email}",
        body_html=approval_request_email_message(username=user.username, email=user.email, subject=message.subject,
                                                 body=message.body)
    )

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def delete_campaign_contact(request):
    """

    :param request:
    :return:
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
        contact_uid: str = request.data["contact_uid"]
    except KeyError as k:
        logger.critical(f"delete_campaign_contact(): Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Error 404: Could not find this campaign on server."
        })

    if campaign.status != "created":
        return JsonResponseBadRequest(data={
            "message": "Unable to delete contact. Camapign not in 'created' state."
        })

    try:
        contact: CampaignContact = CampaignContact.objects.get(campaign=campaign, uid=contact_uid)
    except CampaignContact.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Error 404: Could not find contact in campaign on server."
        })

    contact.delete()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def schedule_campaign_start(request):
    """
    API for setting scheduled start datetime for a given campaign. Should be at least 5 minutes in future.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
        start_datetime_str: str = request.data["start_datetime"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Failed to schedule campaign: Could not find this campaign on server."
        })

    # Check if there are enough credits.
    if campaign.workspace.credits_remaining <= 0:
        return JsonResponseBadRequest(data={
            "message": "Failed to schedule campaign: Not enough credits. "
                       "Please upgrade to a higher tier plan to proceed."
        })

    # Convert ISO string to datetime UTC object
    start_datetime_utc = datetime.fromisoformat(start_datetime_str).astimezone(pytz.UTC)

    # Check if given datetime is at least 5 minutes in future
    current_time = timezone.now()
    if (start_datetime_utc - current_time).total_seconds() < 300:
        return JsonResponseBadRequest(data={
            "message": "Failed to schedule campaign: Please set the date & time to be at least 5 minutes in future"
        })

    # Check usable date. Find the biggest date and check if today is past that date.
    max_domain_usable_from = max([msub.domain_usable_from for msub in campaign.sending_domains.all()])
    if start_datetime_utc < max_domain_usable_from:
        return JsonResponseBadRequest(data={
            "message": f"Selected domain is not "
                       f"usable until {max_domain_usable_from.strftime('%d %B %Y %H:%M %Z')}."
        })

    # Check for email message validity.
    check_result = are_campaign_emails_valid(campaign)
    if not check_result.valid:
        return JsonResponseBadRequest(data={
            "message": f"Failed to schedule campaign: {check_result.reason}"
        })

    campaign.scheduled_start_datetime = start_datetime_utc
    campaign.status = "scheduled"
    campaign.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def delete_campaign_custom_start_datetime(request):
    """
    API to remove scheduled start datetime from campaign.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Error 404: Could not find this campaign on server."
        })

    campaign.scheduled_start_datetime = None
    campaign.status = "created"
    campaign.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def start_campaign(request):
    """
    API for starting campaign that is currently in "created" state.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # Fetch the campaign.
    try:
        campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"add_campaign_email_message() - Campaign with UID {campaign_uid} does not exist in "
                     f"account {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    # Check if credits are available.
    if campaign.workspace.credits_remaining <= 0:
        logger.info(f"Failed to start campaign {campaign_uid} due to lack of credits.")
        return JsonResponseBadRequest(data={
            "message": "No credits are available. Please upgrade to a higher tier plan to proceed."
        })

    # Check if all email messages are proper.
    check_result = are_campaign_emails_valid(campaign)
    if not check_result.valid:
        logger.info(f"Failed to start campaign {campaign_uid}: {check_result.reason}")
        return JsonResponseBadRequest(data={
            "message": check_result.reason
        })

    # Check usable date. Find the biggest date and check if today is past that date.
    max_domain_usable_from = max([msub.domain_usable_from for msub in campaign.sending_domains.all()])
    if timezone.now() < max_domain_usable_from:
        logger.info(f"Failed to start campaign {campaign_uid} since one or more of the domains "
                    f"are not usable right now.")
        return JsonResponseBadRequest(data={
            "message": f"Some of the selected domains are not usable "
                       f"until {max_domain_usable_from.strftime('%d %B %Y %H:%M %Z')}."
        })

    # Start celery task.
    start_campaign_task.delay(campaign_uid)

    # Update campaign status.
    campaign.is_generating_schedules = True
    campaign.status = "running"
    campaign.save()

    return JsonResponseSuccess(data={
        "campaign_uid": campaign_uid,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def pause_campaign(request):
    """
    API for pausing campaign that is currently in "running" state.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # Fetch the campaign.
    try:
        campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"pause_campaign() - Campaign with UID {campaign_uid} does not exist in "
                     f"account {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    # Check if schedules are being generated. If so don't pause.
    if campaign.is_generating_schedules:
        logger.warning(f"Failed to pause campaign {campaign_uid}: is_generating_schedules is True")
        return JsonResponseBadRequest(data={
            "message": "Hold On! Your scheduled emails are still being generated. "
                       "Please wait for current operations to finish before pausing the campaign."
        })

    # Start the pause campaign celery task.
    pause_campaign_task.delay(campaign_uid)

    # We'll also update status here so changes are visible for user immediately.
    campaign.status = "paused"
    campaign.campaign_paused = True
    campaign.campaign_paused_on = timezone.now()
    campaign.is_deleting_schedules = True
    campaign.save()

    # Send email to user
    send_email_task.delay(
        to=user.email,
        sender="<EMAIL>",
        sender_name="Team DeliverymanAI",
        subject=f"Your campaign \"{campaign.name}\" has been paused",
        body_html=campaign_paused_email_message(
            username=user.username,
            campaign_name=campaign.name,
            campaign_details_url=os.environ["CE_APP_HOST_URL"] + f"/campaigns/{campaign_uid}",
            current_year=str(datetime.now().year),
        )
    )

    return JsonResponseSuccess(data={
        "campaign_uid": campaign_uid,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def resume_campaign(request):
    """
    API for resuming campaign that is currently in "paused" state.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # Fetch the campaign.
    try:
        campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"resume_campaign() - Campaign with UID {campaign_uid} does not exist in "
                     f"account {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    # Check if schedules are being deleted. If so don't resume right now.
    if campaign.is_deleting_schedules:
        logger.warning(f"Failed to resume campaign {campaign_uid}: is_generating_schedules is True")
        return JsonResponseBadRequest(data={
            "message": "Hold On! Your scheduled emails are still being deleted. "
                       "Please wait for current operations to finish before resuming the campaign."
        })

    # Check credits.
    if campaign.workspace.credits_remaining <= 0:
        logger.info(f"Failed to resume campaing {campaign_uid}: Not enough credits.")
        return JsonResponseBadRequest(data={
            "message": "No credits are available. Please upgrade to a higher tier plan to proceed."
        })

    # Start the resume campaign celery task.
    resume_campaign_task.delay(campaign_uid)

    # We'll also update status here so changes are visible for user immediately.
    campaign.status = "running"
    campaign.campaign_paused = False
    campaign.is_generating_schedules = True
    campaign.save()

    # Send email to user
    send_email_task.delay(
        to=user.email,
        sender="<EMAIL>",
        sender_name="Team DeliverymanAI",
        subject=f"Your campaign \"{campaign.name}\" has resumed",
        body_html=campaign_resumed_email_message(
            username=user.username,
            campaign_name=campaign.name,
            campaign_details_url=os.environ["CE_APP_HOST_URL"] + f"/campaigns/{campaign_uid}",
            current_year=str(datetime.now().year),
        )
    )

    return JsonResponseSuccess(data={
        "campaign_uid": campaign_uid,
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def mark_email_sent(request):
    """
    API for marking CampaignSchedule as sent.
    """
    try:
        uid: str = request.data["uid"]
        status: str = request.data["status"]
        message_id: str = request.data["message_id"]
    except KeyError as k:
        logger.critical(f"start_campaign_email_message() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        schedule = CampaignSchedule.objects.get(uid=uid)
        user = schedule.campaign.workspace.user
    except CampaignSchedule.DoesNotExist:
        logger.critical(f"mark_email_sent() - Cannot find CampaignSchedule with UID {uid}")
        return JsonResponseNotFound()

    if status == "success":
        schedule.message_id = message_id
        schedule.status = "sent"
        schedule.sent_on = timezone.now()

        workspace = schedule.campaign.workspace
        workspace.total_emails_sent += 1
        workspace.credits_remaining = max(workspace.credits_remaining - 1, 0)
        workspace.save()

        msub: ManagedSubdomain = schedule.contact.sending_email.email_subdomain.managed_subdomain
        if (msub.email_limit_last_updated_on is None) or (
                (timezone.now() - msub.email_limit_last_updated_on).days >= 1):
            # Update limit and last_updated_on date.
            rand_percent = random.randint(DAILY_EMAIL_LIMIT_PERCENT_INCR_MIN, DAILY_EMAIL_LIMIT_PERCENT_INCR_MAX)
            msub.email_limit = min(
                math.floor((msub.email_limit * rand_percent / 100) + msub.email_limit),
                MAX_DAILY_EMAIL_LIMIT_CAP
            )
            msub.email_limit_last_updated_on = timezone.now()
            msub.save()

        # If credits are 0, pause this campaign. We only need to do this once.
        if (schedule.campaign.status != "paused") and (workspace.credits_remaining <= 0):
            logger.debug(f"Credits are 0, pausing campaign for '{schedule.campaign.name}'")
            pause_campaign_task.delay(schedule.campaign.uid)

            # Send email to user.
            # Send email to user.
            send_email_task.delay(
                to=user.email,
                sender="<EMAIL>",
                sender_name="Team DeliverymanAI",
                subject=f"You’ve run out of email credits for workspace \"{workspace.name}\"",
                body_html=credits_exhausted_email_message(
                    username=user.username,
                    plan_page_url=os.environ["CE_APP_HOST_URL"] + "/manage-subscription",
                    workspace_name=workspace.name,
                    current_year=str(datetime.now().year),
                )
            )

    else:
        schedule.status = "failed"

    schedule.save()

    # Check for campaign end.
    campaign: Campaign = schedule.campaign
    if campaign.campaignschedule_set.filter(status__in=["created", "scheduled"]).count() == 0:
        logger.debug(f"Campaign {campaign.uid} has been completed successfully!")
        campaign.status = "complete"
        campaign.save()

        # Mark all contacts in this campaign as inactive.
        for contact in campaign.campaigncontact_set.all():
            contact.active = False
            contact.save()

        # Send email to user.
        if not user.has_sent_campaign_completed_email:
            total_contacts = campaign.campaigncontact_set.count()
            total_emails_sent: int = campaign.campaignschedule_set.exclude(sent_on=None).count()
            total_replies_received: int = campaign.campaignschedule_set.filter(reply_received=True).count()
            total_bounced: int = campaign.bounces

            send_email_task.delay(
                to=user.email,
                sender="<EMAIL>",
                sender_name="Team DeliverymanAI",
                subject=f"Your campaign \"{campaign.name}\" is complete",
                body_html=campaign_completed_email_message(
                    username=user.username,
                    emails_sent_count=total_emails_sent,
                    reply_rate=f"{total_replies_received / total_contacts:.2f}",
                    bounce_rate=f"{total_bounced / total_contacts:.2f}",
                    campaign_details_url=os.environ["CE_APP_HOST_URL"] + f"/campaigns/{campaign.uid}",
                    current_year=str(datetime.now().year),
                )
            )

            user.has_sent_campaign_completed_email = True
            user.save()

            logger.info(f"Sent campaign completed email to user {user.email}")

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([AllowAny])
def get_schedule_email_status(request):
    """
    Returns current status of schedule.
    """
    try:
        schedule_uid: str = request.query_params["uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email_message() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        schedule = CampaignSchedule.objects.get(uid=schedule_uid)
    except CampaignSchedule.DoesNotExist:
        logger.critical(f"mark_email_sent() - Cannot find CampaignSchedule with UID {schedule_uid}")
        return JsonResponseNotFound()

    return JsonResponseSuccess(data={
        "status": schedule.status
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_details(request):
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"get_campaign_emails() - Campaign with uid {campaign_uid} not found in account {user.email}")
        return JsonResponseNotFound(data={"message": f"Could not find resource on server."})

    return JsonResponseSuccess(data={
        "name": campaign.name,
        "status": campaign.status,
        "custom_start_datetime": campaign.scheduled_start_datetime,
        "time_zone": user.active_workspace.time_zone
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_analytics(request):
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"get_campaign_emails() - Campaign with uid {campaign_uid} not found in account {user.email}")
        return JsonResponseNotFound(data={"message": f"Could not find resource on server."})

    total_emails_sent: int = campaign.campaignschedule_set.exclude(sent_on=None).count()
    total_replies_received: int = campaign.campaignschedule_set.filter(reply_received=True).count()
    # total_bounced: int = campaign.campaignschedule_set.filter(status="cancelled_bad_email").count()
    total_bounced: int = campaign.bounces
    latest_activity = (
        CampaignActivity.objects
        .annotate(
            rn=Window(
                expression=RowNumber(),
                partition_by=[F("campaign_id"), F("event_from")],
                order_by=F("event_date").desc(),
            )
        )
        .filter(rn=1)
        .values("campaign")
        .annotate(
            unsub_count=Count(
                "event_from",
                filter=Q(event_type="unsubscribe"),
                distinct=True,
            )
        )
        .values("campaign", "unsub_count")
    )
    unsub_count_subquery = Subquery(
        latest_activity.filter(campaign=OuterRef("pk")).values("unsub_count")[:1]
    )
    campaign_with_stats = (
        Campaign.objects.annotate(total_unsubscribed=unsub_count_subquery)
        .get(pk=campaign.pk)
    )

    return JsonResponseSuccess(data={
        "campaign_name": campaign.name,
        "emails_per_day": campaign.emails_per_day,
        "total_contacts": campaign.campaigncontact_set.count(),
        "total_schedules": campaign.campaignschedule_set.count(),
        "total_emails_sent": total_emails_sent,
        "total_bounced": total_bounced,
        "total_unsubscribes": campaign_with_stats.total_unsubscribed or 0,
        "total_replies": total_replies_received,
        "campaign_status": campaign.status,
        "campaign_activities": CampaignActivitySerializer(
            campaign.campaignactivity_set.all().order_by('-event_date'),
            many=True
        ).data,
        "domains": [domain.subdomain for domain in campaign.sending_domains.all()]
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_contacts(request):
    """

    :param request:
    :return:
    """
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"get_campaign_emails() - Campaign with uid {campaign_uid} not found in account {user.email}")
        return JsonResponseNotFound(data={"message": f"Could not find resource on server."})

    leads_count: int = campaign.campaigncontact_set.count()
    bad_emails_count: int = campaign.campaignignoredcontacts_set.count()
    schedules_count: int = campaign.campaignschedule_set.count()

    return JsonResponseSuccess(data={
        "leads_count": leads_count,
        "bad_emails_count": bad_emails_count,
        "schedules_count": schedules_count,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_leads(request):
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"get_campaign_emails() - Campaign with uid {campaign_uid} not found in account {user.email}")
        return JsonResponseNotFound(data={"message": f"Could not find resource on server."})

    leads: List[Dict] = []
    for contact in campaign.campaigncontact_set.select_related("sending_email").all().order_by("created_on"):
        leads.append({
            "uid": contact.uid,
            "email_id": contact.email_id,
            "attributes": contact.attributes or None,
            "sending_email": contact.sending_email.email_address if contact.sending_email else None,
        })

    return JsonResponseSuccess(data={
        "leads": leads
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_schedules(request):
    """
    API for campaign details "schedules" tab.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"get_campaign_emails() - Campaign with uid {campaign_uid} not found in account {user.email}")
        return JsonResponseNotFound(data={"message": f"Could not find resource on server."})

    if campaign.is_generating_schedules:
        return JsonResponseSuccess(data={
            "generating": True,
            "schedules": []
        })

    schedules: List[Dict] = []
    for schedule in campaign.campaignschedule_set.select_related(
            "contact", "contact__sending_email", "email_message"
    ).all().order_by("schedule_datetime"):

        if schedule.schedule_datetime:
            if campaign.status == "paused":
                email_scheduled_ts = None
            else:
                email_scheduled_ts = schedule.schedule_datetime.timestamp() * 1000
        else:
            email_scheduled_ts = None

        schedules.append({
            "id": schedule.id,
            "recipient": schedule.contact.email_id,
            "sender_email": schedule.contact.sending_email.email_address if schedule.contact.sending_email else "[DELETED]",
            "sender_name": schedule.contact.sending_email.username if schedule.contact.sending_email else "[DELETED]",
            "subject": schedule.email_message.subject,
            "email_status": schedule.status,
            "email_sent_on_ts": schedule.sent_on.timestamp() * 1000 if schedule.sent_on else None,
            "email_scheduled_ts": email_scheduled_ts,
            "reply_received": schedule.reply_received,
            "reply_email_s3_key": schedule.email_s3_key,
        })

    return JsonResponseSuccess(data={
        "generating": False,
        "schedules": schedules
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_ignored_emails(request):
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"get_campaign_emails() - Campaign with uid {campaign_uid} not found in account {user.email}")
        return JsonResponseNotFound(data={"message": f"Could not find resource on server."})

    bad_emails: List[Dict] = []
    for email in campaign.campaignignoredcontacts_set.all():
        bad_emails.append({
            "id": email.id,
            "email_id": email.email_id,
            "reason": email.reason,
        })

    return JsonResponseSuccess(data={
        "ignored_emails": bad_emails
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def cancel_campaign(request):
    """
    API for stopping campaign.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
    except KeyError as k:
        logger.critical(f"start_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # Fetch the campaign.
    try:
        campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        logger.error(f"add_campaign_email_message() - Campaign with UID {campaign_uid} does not exist in "
                     f"account {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    # Check if schedules are being generated. If so avoid cancelling until that is done.
    if campaign.is_generating_schedules:
        logger.warning(f"Failed to cancel campaign {campaign_uid}: is_generating_schedules is True")
        return JsonResponseBadRequest(data={
            "message": "Hold On! Your scheduled emails are still being generated. "
                       "Please wait for current operations to finish before you cancel the campaign."
        })

    # Check for current campaign status.
    if campaign.status in ["creating", "complete", "cancelled"]:
        return JsonResponseBadRequest(data={
            "message": f"Campaign cannot be cancelled in current state: {campaign.status}"
        })

    # Start celery task.
    cancel_campaign_task.delay(campaign_uid)

    # We'll manually change status here so that user sees it as cancelled immediately.
    campaign.is_deleting_schedules = True
    campaign.scheduled_start_datetime = None
    campaign.status = "cancelled"
    campaign.save()

    return JsonResponseSuccess(data={
        "campaign_uid": campaign_uid
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def save_email_s3_key(request):
    """
    API to save S3 key of reply email message stored for this schedule.
    """
    try:
        s3_key: str = request.data["s3_key"]
        reply_to_message_id: str = request.data["reply_to_message_id"]
    except KeyError as k:
        logger.critical(f"save_email_s3_key() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    if not s3_key:
        logger.error(f"save_email_s3_key() - Invalid S3 key {s3_key}")
        return JsonResponseBadRequest(data={"message": f"Invalid S3 key '{s3_key}'"})

    try:
        schedule: CampaignSchedule = CampaignSchedule.objects.get(message_id=reply_to_message_id)
    except CampaignSchedule.DoesNotExist:
        logger.error(f"save_email_s3_key() - Could not find CampaignSchedule with message_id '{reply_to_message_id}'")
        return JsonResponseNotFound(data={
            "message": f"Could not find CampaignSchedule with message_id '{reply_to_message_id}'"
        })

    schedule.email_s3_key = s3_key
    schedule.save()

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([AllowAny])
def get_campaign_reply_to_address(request):
    """
    API to fetch email forwarding destination email address from given campaign schedule.
    """
    try:
        reply_to_message_id: str = request.query_params["reply_to_message_id"]
    except KeyError as k:
        logger.critical(f"get_campaign_reply_to_address() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        schedule: CampaignSchedule = CampaignSchedule.objects.get(message_id=reply_to_message_id)
    except CampaignSchedule.DoesNotExist:
        logger.error(
            f"get_campaign_reply_to_address() - Could not find CampaignSchedule with message_id '{reply_to_message_id}'")
        return JsonResponseNotFound(data={
            "message": f"Could not find CampaignSchedule with message_id '{reply_to_message_id}'"
        })

    return JsonResponseSuccess(data={
        "email_id": schedule.campaign.reply_to_address,
    })


@api_view(["GET"])
@permission_classes([AllowAny])
def get_campaign_account_email(request):
    """
    API to fetch campaign owner's account email id using given campaign email address.
    """
    try:
        campaign_email: str = request.query_params["campaign_email"]
    except KeyError as k:
        logger.critical(f"get_user_account_email_from_campaign_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        msub: ManagedSubdomain = EmailID.objects.get(email_address=campaign_email).email_subdomain.managed_subdomain
    except EmailID.DoesNotExist:
        logger.error(f"get_user_account_email_from_campaign_email() - "
                     f"Could not find EmailID with address '{campaign_email}'")
        return JsonResponseNotFound(data={
            "message": f"Could not find EmailID with address '{campaign_email}'"
        })

    user: User = msub.workspace.user

    return JsonResponseSuccess(data={
        "email_id": user.email,
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def campaign_email_reply_received_webhook(request):
    """
    This webhook HTTPS endpoint is called by Amazon SNS service when a reply is received on
    CE_REPLY_TO_ADDRESS email id. Marks this schedule as replied and cancels any existing future schedule for this
    contact email id.

    If the schedule was already marked as replied, the event is ignored.
    """
    # Start the reply handler task.
    camapign_reply_handler_task.delay(request.data["Message"])

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([AllowAny])
def campaign_unsubscribe_contact(request):
    try:
        email_id: str = request.query_params["email"]
        campaign_uid: str = request.query_params["cid"]
    except KeyError as k:
        logger.critical(f"campaign_unsubscribe_contact() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        cm = CampaignManager(campaign_uid)
    except CampaignNotFound:
        logger.error(f"campaign_unsubscribe_contact() - Could not find campaign {campaign_uid}")
        return JsonResponseBadRequest(data={"message": "No such campaign."})

    logger.debug(f"Unsubcribing contact {email_id} from campaign {campaign_uid}...")
    try:
        cm.unsubscribe_contact(email_id=email_id)
    except CampaignContactNotFound as err:
        logger.error(f"campaign_unsubscribe_contact() - {err}")
        return JsonResponseBadRequest(data={"message": "No such contact."})

    return JsonResponseSuccess()


@api_view(["POST"])
def mark_bad_email(request):
    """
    API that marks given email address as a "bad email" and cancels all active schedules with this email id
    across all campaigns.
    """
    try:
        bad_email_id: str = request.data["bad_email_id"].strip().lower()
        reason: str = request.data["reason"].strip()
    except KeyError as k:
        logger.critical(f"mark_bad_email() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    logger.debug(f"Bad Email ID: {bad_email_id}")
    logger.debug(f"Reason: {reason}")

    # Add this to bad email list.
    try:
        BadEmail.objects.create(
            email_id=bad_email_id,
            reason=reason,
        )
    except IntegrityError:
        logger.info(f"Email id {bad_email_id} already exists in BadEmail table.")

    # For Bounce notification and counter.
    # Should hold unique list of campaigns that are going to be affected by this email.
    # We do this so that followup schedule deletion doesn't create duplicate events and increment value incorrectly.
    affected_campaigns: List[Campaign] = []

    # Find all "created" and "sent" schedules across all campaigns and cancel them.
    schedules = CampaignSchedule.objects.filter(status__in=["created", "scheduled", "sent"],
                                                contact__email_id=bad_email_id)
    for schedule in schedules:
        if schedule.campaign not in affected_campaigns:
            affected_campaigns.append(schedule.campaign)

        # Delete schedule if it has not been sent already.
        if schedule.status == "created":
            try:
                aws_eventbridge.delete_schedule(
                    Name=schedule.schedule_name,
                )
            except botocore.exceptions.ClientError as err:
                # Only skip the error if it's due to missing schedule.
                if err.response['Error']['Code'] != "ResourceNotFoundException":
                    raise err

        # Update the contact.
        schedule.contact.active = False
        schedule.contact.save()

        # Mark contact list contact as bounced.
        if schedule.contact.imported_contact_list_contact:
            schedule.contact.imported_contact_list_contact.bounced = True
            schedule.contact.imported_contact_list_contact.save()

        # Update status.
        schedule.status = "cancelled_bad_email"
        schedule.save()

        logger.debug(f"Schedule {schedule.schedule_name} has been deleted. Reason: bad email.")

    logger.debug(affected_campaigns)

    # For all affected campaigns, create a Bounce activity log and increment their bounce counter.
    for camp in affected_campaigns:
        logger.debug(f"Creating bounce / bad email activity log for {bad_email_id} in campaign {camp.uid}...")
        CampaignActivity.objects.create(
            campaign=camp,
            event_subject="Bounce",
            event_from=bad_email_id,
        )
        camp.bounces += 1
        camp.save()

    return JsonResponseSuccess()


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def campaign_settings(request):
    """
    API to get Campaign settings (GET) and save campaign settings data (POST).
    """
    user: User = request.user

    if request.method == "GET":
        try:
            campaign_uid: str = request.query_params["campaign_uid"]
        except KeyError as k:
            logger.critical(f"campaign_unsubscribe_contact() - Missing key {k}")
            return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

        try:
            campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
        except Campaign.DoesNotExist:
            logger.error(f"campaign_settings() - Could not find campaign {campaign_uid} for user {user.email}")
            return JsonResponseNotFound(data={
                "message": "No such campaign.",
            })

        # sending_domains: List[str] = list(campaign.sending_domains.all().values_list("subdomain", flat=True))
        # Fetch domains selected for thsi campaign.
        sending_domains: List[Dict] = []
        for msub in campaign.sending_domains.all():
            sending_domains.append({
                "id": str(msub.id),
                "domain": msub.subdomain,
                "checked": True,
            })

        # Fetch domains that are:
        # - not in initial warmup state
        # - not in current campaign.
        # - not in any other running/created campaigns.
        excluded_statuses = ["creating", "created", "scheduled", "running", "paused"]
        available_domains: List[Dict] = []
        for msub in user.active_workspace.managedsubdomain_set.filter(setup_complete=True).exclude(
                campaign__status__in=excluded_statuses
        ):
            available_domains.append({
                "id": str(msub.id),
                "domain": msub.subdomain,
                "checked": False,
            })

        # Combine both lists.
        domains = [*sending_domains, *available_domains]

        # Calculate maximum daily email limit.
        max_emails_daily: int = campaign.sending_domains.aggregate(Sum("email_limit"))["email_limit__sum"]

        return JsonResponseSuccess(data={
            "reply_to_address": campaign.reply_to_address,
            "max_emails_daily": max_emails_daily,
            "managed_subdomains": domains,
            "skip_days": campaign.skip_days,
            "test_email_target": campaign.test_email_destinations,
            "is_html_email": campaign.is_html_email,
            "add_unsub_link": campaign.add_unsub_link,
            "unsub_link_type": campaign.unsub_link_type,
            "custom_unsub_text": campaign.custom_unsub_text,
        })

    else:
        try:
            campaign_uid: str = request.data["campaign_uid"]
            reply_to_address: str = html.escape(request.data["reply_to_address"].strip().lower())
            selected_domain_ids: List[int] = request.data["selected_domain_ids"]
            skip_days: List[str] = request.data["skip_days"]
            test_email_target: List[str] = request.data["test_email_target"]
            add_unsub_link: bool = request.data["add_unsub_link"]
            unsub_link_type: str = request.data["unsub_link_type"]
            custom_unsub_text: str = request.data["custom_unsub_text"].strip()
        except KeyError as k:
            logger.critical(f"campaign_unsubscribe_contact() - Missing key {k}")
            return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

        # check the number of skipped days.
        if len(skip_days) >= 7:
            return JsonResponseBadRequest(data={
                "message": "You cannot skip all days in a week."
            })

        if len(selected_domain_ids) == 0:
            return JsonResponseBadRequest(data={
                "message": "Please select at least one domain for sending emails in this campaign."
            })

        if len(custom_unsub_text) > 100:
            return JsonResponseBadRequest(data={
                "message": "Save Failed: Please keep custom unsubscribe text less than 100 characters long."
            })

        try:
            campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
        except Campaign.DoesNotExist:
            logger.error(f"campaign_settings() - Could not find campaign {campaign_uid} for user {user.email}")
            return JsonResponseNotFound(data={
                "message": "No such campaign.",
            })
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        # ----------- Update reply to address -----------
        if not reply_to_address:
            campaign.reply_to_address = user.email
        else:
            if not re.match(email_regex, reply_to_address):
                return JsonResponseBadRequest(data={
                    "message": "Please enter a valid reply-to email address or leave it empty."
                })
            campaign.reply_to_address = reply_to_address

        # ----------- Update test email destinations -----------
        for email in test_email_target:
            if not re.match(email_regex, email):
                return JsonResponseBadRequest(data={
                    "message": "One or more of your test email destinations is invalid."
                })
        campaign.test_email_destinations = test_email_target

        # ----------- Update skip days -----------
        campaign.skip_days = skip_days
        campaign.add_unsub_link = add_unsub_link
        campaign.unsub_link_type = unsub_link_type
        campaign.custom_unsub_text = custom_unsub_text

        # ----------- Update sending domains -----------
        if campaign.status == "created":
            campaign.sending_domains.clear()
            for msub_id in selected_domain_ids:
                try:
                    msub = user.active_workspace.managedsubdomain_set.get(id=msub_id)
                except ManagedSubdomain.DoesNotExist:
                    # skip this
                    continue

                campaign.sending_domains.add(msub)

        campaign.save()

        return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_reply_email_message(request):
    """
    API that returns email body plain text message for given s3 key, if it exists on logged in user account.
    :param request:
    :return:
    """
    user: User = request.user

    try:
        s3_key: str = request.query_params["key"]
    except KeyError as k:
        logger.critical(f"get_email_reply_message() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    # Check if this key exists in any campaign for this user.
    if not user.active_workspace.campaign_set.filter(campaignschedule__email_s3_key=s3_key).exists():
        logger.critical(f"get_email_reply_message() - No schedule with S3 key '{s3_key}' was found.")
        return JsonResponseBadRequest(data={
            "message": "No such email reply was found."
        })

    # Fetch the email body from S3
    response = aws_s3_client.get_object(
        Bucket=os.environ["CE_REPLY_S3_BUCKET_NAME"],
        Key=s3_key
    )
    streaming_body = response["Body"]
    content = streaming_body.read()

    plain_text, html_content = get_email_body(content)

    return JsonResponseSuccess(data={
        "message": plain_text or html_content or "No message data available.",
    })


@api_view(["POST"])
@permission_classes([AllowAny])
def webhook_email_verify(request):
    """
    Email verify results are sent here.
    """
    data: List[List] = request.data["results"]  # ["email", {verification_data}]
    task_uid: str = request.data["task_uid"]

    # Go through each item in data and add them to BadEmail list if they are not deliverable.
    for item in data:
        email_id: str = item[0]
        result: Dict = item[1]

        if (result is None) or (not result["deliverable"]) or (result["catch_all"]):
            try:
                BadEmail.objects.create(
                    email_id=email_id,
                    reason="Could not verify email address."
                )
            except IntegrityError:
                logger.warning(f"{email_id} already present in BadEmail list. Skipping...")

    # Fetch the contacts data from Redis
    with redis.Redis(
            host=os.environ["CE_REDIS_HOST"],
            port=int(os.environ["CE_REDIS_PORT"]),
            db=int(os.environ["CE_REDIS_DB"])
    ) as redis_client:
        redis_data = json.loads(redis_client.get(task_uid))
        user_id: int = redis_data["user_id"]
        campaign_uid: str = redis_data["campaign_uid"]
        email_key: str = redis_data["email_key"]
        converted_contacts_data: List[Dict] = redis_data["data"]

        user: User = User.objects.get(id=user_id)
        campaign: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)

        for contact in converted_contacts_data:
            # We'll store all "attribute" data (i.e. contact data without email column) in attributes_json.
            attributes_json: Dict = {}

            try:
                email_id: str = html.escape(contact[email_key].strip().lower())
            except KeyError:
                # If email value is missing, ignore this one.
                logger.warning(f"create_campaign() - Missing email in contact data. Skipping...")
                continue

            # skip if blank.
            if not email_id:
                logger.warning(f"create_campaign() - Blank email in contact data. Skipping...")
                continue

            # Add all except for email column as attributes.
            for attribute in contact.keys():
                if attribute != email_key:
                    attributes_json.update({attribute: contact[attribute]})

            # unsubscribed_emails: List[str] = UnsubscribedEmail.objects.filter(
            #     workspace=user.active_workspace,
            #     email_id=email_id
            # ).values_list("email_id", flat=True)
            #
            # bad_emails: List[str] = BadEmail.objects.all().values_list("email_id", flat=True)

            # Create the contacts object.
            contact_uid = f"contact_{uuid.uuid4().hex}"
            CampaignContact.objects.create(
                uid=contact_uid,
                campaign=campaign,
                email_id=email_id,
                attributes=attributes_json
            )
            logger.debug(f"Created contact {email_id} for campaign {campaign_uid}")

            # if email_id in unsubscribed_emails:
            #     CampaignIgnoredContacts.objects.create(
            #         campaign=campaign,
            #         email_id=email_id,
            #         reason="Unsubscribed from your campaigns."
            #     )
            #     logger.debug(f"Added contact {email_id} to ignored list for being unsubscribed.")
            #
            # elif email_id in bad_emails:
            #     CampaignIgnoredContacts.objects.create(
            #         campaign=campaign,
            #         email_id=email_id,
            #         reason="Bad email address / other delivery issues."
            #     )
            #     logger.debug(f"Added contact {email_id} to ignored list for being a bad email.")
            #
            # else:
            #     CampaignContact.objects.create(
            #         uid=contact_uid,
            #         campaign=campaign,
            #         email_id=email_id,
            #         attributes=attributes_json
            #     )
            #     logger.debug(f"Created contact {email_id} for campaign {campaign_uid}")

        # Delete the contact data in redis.
        redis_client.delete(task_uid)

    # Update campaign status.
    campaign.status = "created"
    campaign.save()

    # Send websocket message.
    time.sleep(5)
    send_websocket_event(
        f"campaigns_page_{campaign.workspace.user.id}",
        "campaign_status_update",
        "campaign_status_update",
        {
            "campaign_uid": campaign_uid,
            "new_status": "created",
        }
    )

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def integrations(request):
    """
    API that returns data for integrations page.
    """
    user: User = request.user

    return JsonResponseSuccess(data={
        "google_auth_active": user.google_api_auth_creds is not None
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def google_postmaster_integration(request):
    """
    API that returns data for google postmaster integration page.
    """
    user: User = request.user

    postmaster_domains: List[Dict] = [
        {"domain": domain["domain"], "added_on_ts": domain["added_on_ts"]}
        for domain in user.google_postmaster_integration.domains_added
    ]

    return JsonResponseSuccess(data={
        "domains": postmaster_domains,
        "available_managed_domains": list(user.active_workspace.managedsubdomain_set.exclude(
            subdomain__in=[domain["domain"] for domain in postmaster_domains]
        ).values_list("subdomain", flat=True))
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def google_postmaster_add_domain(request):
    """
    API to add domain to Google Postmaster integration. Requires the domain name and TXT value generated by Google
    Postmaster.
    """
    user: User = request.user

    try:
        domain: str = html.escape(request.data["domain"].lower().strip())
        txt_value: str = request.data["TXT"].strip()
    except KeyError as k:
        logger.critical(f"google_postmaster_add_domain() - Missing key '{k}' in request.")
        return JsonResponseBadRequest(data={
            "message": f"Missing key '{k}' in request."
        })

    # Check if postmaster integration is active.
    if user.google_postmaster_integration is None:
        return JsonResponseBadRequest(data={
            "message": "Google Postmaster integration is not active on your current account."
        })

    # Check if domain and txt have valid values.
    if not (domain and txt_value):
        logger.error(f"google_postmaster_add_domain() - domain: '{domain}', TXT: '{txt_value}'")
        return JsonResponseBadRequest(data={
            "message": "Invalid domain and/or TXT value."
        })

    # Check if this domain was already added to google postmaster integration.
    if domain in [dom["domain"] for dom in user.google_postmaster_integration.domains_added]:
        return JsonResponseBadRequest(data={
            "message": f"Domain '{domain}' has already been added to Google Postmaster integration."
        })

    # Fetch and check if domain is present in user's managed subdomains.
    try:
        msub: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=domain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"google_postmaster_add_domain() - Domain/subdomain '{domain}' does not exist in user's account.")
        return JsonResponseBadRequest(data={
            "message": "This domain/subdomain does not exist in your Deliveryman.ai account."
        })

    # Add the TXT record.
    logger.debug(f"Adding TXT record to {domain} (hosted zone id: {msub.hosted_zone_id})...")
    logger.debug(f"TXT Record: {txt_value}")
    try:
        add_route53_record(
            hosted_zone_id=msub.hosted_zone_id,
            record_type="TXT",
            record_name=msub.subdomain + ".",
            record_value=f"\"{txt_value}\"",
        )
    except Exception as err:
        logger.critical(
            f"google_postmaster_add_domain() - Failed to add Route53 TXT record. Error: {err}",
            exc_info=True
        )
        return JsonResponseServerError(data={
            "message": "Server error. Please try again later and contact us if the issue persists."
        })

    # Add this domain to postmaster integration domain list.
    current_domain_list: List[GooglePostmasterDomainSchema] = [
        GooglePostmasterDomainSchema(**item) for item in user.google_postmaster_integration.domains_added
    ]
    current_domain_list.append(GooglePostmasterDomainSchema(
        domain=domain,
        added_on_ts=timezone.now().timestamp() * 1000,
        txt_value=txt_value,
    ))

    user.google_postmaster_integration.domains_added = [item.model_dump() for item in current_domain_list]
    user.google_postmaster_integration.save()
    user.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def google_postmaster_remove_domain(request):
    """
    API to remove domain from Google Postmaster integration. Requires the domain name.
    """
    user: User = request.user

    try:
        domain: str = html.escape(request.data["domain"].lower().strip())
    except KeyError as k:
        logger.critical(f"google_postmaster_remove_domain() - Missing key '{k}' in request.")
        return JsonResponseBadRequest(data={
            "message": f"Missing key '{k}' in request."
        })

    # Fetch managed subdomain.
    try:
        msub: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=domain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"google_postmaster_remove_domain() - Managed subdomain '{domain}' does not "
                     f"exist {user.email} account.")
        return JsonResponseNotFound(data={
            "message": "Could not find resource on server."
        })

    # Fetch the domain data in integration.
    current_domain_list: List[GooglePostmasterDomainSchema] = [
        GooglePostmasterDomainSchema(**item) for item in user.google_postmaster_integration.domains_added
    ]

    # Delete TXT record on Route53
    try:
        domain_data: GooglePostmasterDomainSchema = list(filter(lambda x: x.domain == domain, current_domain_list))[0]
        delete_route53_records(msub.hosted_zone_id, [{
            'Name': msub.subdomain,
            'Type': "TXT",
            'TTL': 60,
            'ResourceRecords': [
                {
                    'Value': f"\"{domain_data.txt_value}\""
                }
            ]
        }])

    except Exception as err:
        logger.critical(err, exc_info=True)
        return JsonResponseServerError(data={
            "message": "Server error. Please try again later and contact us if the issue persists."
        })

    # Remove domain from integration.
    updated_domain_list: List[GooglePostmasterDomainSchema] = list(
        filter(lambda x: x.domain != domain, current_domain_list)
    )

    user.google_postmaster_integration.domains_added = [item.model_dump() for item in updated_domain_list]
    user.google_postmaster_integration.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def domain_redirection_setup(request):
    """
    API to set up domain redirection for given managed domain/subdomain.
    """
    user: User = request.user

    try:
        managed_domain: str = html.escape(request.data["managed_domain"].strip().lower())
        target_domain: str = html.escape(request.data["target_domain"].strip().lower())
    except KeyError as k:
        logger.critical(f"domain_redirection_setup() - Missing key '{k}' in request.")
        return JsonResponseBadRequest(data={
            "message": f"Missing key '{k}' in request."
        })

    # Validate target domain.
    domain_pattern: str = r'^(?!www\.)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$'
    if not bool(re.match(domain_pattern, target_domain)):
        return JsonResponseBadRequest(data={
            "message": "Please provide a valid target domain name (ex. example.com, foo.example.com) without any protocol, "
                       "path or query paramters."
        })

    # Fetch managed subdomain.
    try:
        msub: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=managed_domain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"domain_redirection_setup() - Managed subdomain '{managed_domain}' does not "
                     f"exist {user.email} account.")
        return JsonResponseNotFound(data={
            "message": "Could not find resource on server."
        })

    # Make API call.
    request_data = {
        "managed_subdomain": managed_domain,
        "source_domains": list(msub.emailsubdomain_set.all().values_list('subdomain', flat=True)),
        "target_domain": target_domain,
        "route53_hosted_zone_id": msub.hosted_zone_id,
    }
    res = requests.post(
        url="https://domain-redirect.deliveryman.ai/setup/",
        json=request_data,
        headers={
            "X-API-Key": os.environ["CE_DOMAIN_REDIRECT_API_KEY"]
        }
    )
    if res.status_code != 200:
        logger.error(res.content)
        logger.error(f"Managed Subdomain: {managed_domain}")
        logger.error(f"Target Domain: {target_domain}")
        logger.critical(
            f"Setup Domain Redirection API call failed with status code {res.status_code} for {managed_domain}")
        return JsonResponseServerError(data={
            "message": "Failed to process your request. Please try again later or contact support if "
                       "the issue persists."
        })

    msub.redirect_subdomains_to = target_domain
    msub.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def domain_redirection_delete(request):
    """
    API to delete domain redirection for given managed domain/subdomain.
    """
    user: User = request.user

    try:
        managed_domain: str = html.escape(request.data["managed_domain"].strip().lower())
    except KeyError as k:
        logger.critical(f"domain_redirection_setup() - Missing key '{k}' in request.")
        return JsonResponseBadRequest(data={
            "message": f"Missing key '{k}' in request."
        })

    # Fetch managed subdomain.
    try:
        msub: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=managed_domain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"domain_redirection_delete() - Managed subdomain '{managed_domain}' does not "
                     f"exist {user.email} account.")
        return JsonResponseNotFound(data={
            "message": "Could not find resource on server."
        })

    # Make API call.
    request_data = {
        "managed_subdomain": managed_domain,
        "route53_hosted_zone_id": msub.hosted_zone_id,
    }
    res = requests.post(
        url="https://domain-redirect.deliveryman.ai/delete/",
        json=request_data,
        headers={
            "X-API-Key": os.environ["CE_DOMAIN_REDIRECT_API_KEY"]
        }
    )
    if res.status_code != 200:
        logger.error(res.content)
        logger.error(f"Managed Subdomain: {managed_domain}")
        logger.critical(
            f"Delete Domain Redirection API call failed with status code {res.status_code} for {managed_domain}")
        return JsonResponseServerError(data={
            "message": "Failed to process your request. Please try again later or contact support if "
                       "the issue persists."
        })

    msub.redirect_subdomains_to = None
    msub.save()

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def send_test_email(request):
    """
    API to send a campaign test email to user's account email address.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["uid"].strip()
        email_subject: str = request.data["subject"].strip()
        email_body: str = request.data["body"].strip()
        content_type: str = request.data["content_type"].strip()  # text/plain or text/html
    except KeyError as k:
        logger.critical(f"send_test_email() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Get a random contact to fill in any variables.
    try:
        camp: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find resource on server."
        })

    try:
        contact: CampaignContact = camp.campaigncontact_set.all()[0]
    except IndexError:
        return JsonResponseBadRequest(data={
            "message": "Failed to send test email. No contact found in campaign."
        })

    # Select a random email for sending.
    msub: ManagedSubdomain = camp.sending_domains.all()[0]
    emails = EmailID.objects.filter(email_subdomain__managed_subdomain=msub)
    if emails.count() == 0:
        return JsonResponseBadRequest(data={
            "message": "Failed to send test email. No sending emails found in campaign."
        })
    sending_email: EmailID = emails[0]

    # Prepare the subject and body.
    email_subject = CampaignManager.variable_substitution(email_subject, contact.attributes)
    email_subject = CampaignManager.spintax_substitution(email_subject, "v2")
    email_subject += " [TEST]"

    email_body = CampaignManager.variable_substitution(email_body, contact.attributes)
    email_body = CampaignManager.sender_name_substitution(email_body, sending_email.username)
    email_body = CampaignManager.spintax_substitution(email_body, "v2")

    # Send a test email using AWS send_email with one of their email ids in selected domain.
    try:
        # send_email(
        #     to=camp.test_email_destinations if camp.test_email_destinations else user.email,
        #     sender=sending_email.email_address,
        #     sender_name=sending_email.username,
        #     subject=email_subject,
        #     body_plaintext=email_body,
        #     region_name=os.environ["CE_AWS_REGION"]
        # )

        # Recipent address
        to_addresses: List[str] = camp.test_email_destinations if camp.test_email_destinations else [user.email]

        # Initialize SES client
        ses_client = boto3.client("ses", region_name=os.environ["CE_AWS_REGION"])

        for to_address in to_addresses:
            # Email headers
            headers = [
                ("From", f'"{sending_email.username}" <{sending_email.email_address}>'),
                ("To", f'"{to_address}" <{to_address}>'),
                ("Reply-To", sending_email.email_address),
                ("Subject", email_subject),
                ("Content-Type", f"{content_type}; charset=UTF-8"),
                ("Content-Transfer-Encoding", "8bit"),
                ("X-Auto-Response-Suppress", "All"),
                ("X-MSys-Trusted-Sender", sending_email.email_address),
            ]

            # Build the message.
            raw_message: str = "\n".join([f"{header[0]}: {header[1]}" for header in headers])
            raw_message += f"\n\n{email_body}"

            # Send the test email.
            try:
                response = ses_client.send_raw_email(
                    Source=f"{sending_email.username} <{sending_email.email_address}>",
                    Destinations=[to_address],
                    RawMessage={'Data': raw_message}
                )
                message_id: str = response["MessageId"]
                logger.info(f"Test email sent from {sending_email.email_address} "
                            f"to {to_address} (Message ID: {message_id})")

            except Exception as err:
                logger.critical(f"Failed to send test email: {err}", exc_info=True)

    except Exception as err:
        logger.error(err, exc_info=True)
        logger.critical(f"Failed to send test email: {err}")
        return JsonResponseServerError(data={
            "message": "Failed to send test email due to some issue on our end. Please try again later or contact "
                       "our support if the issue persists."
        })

    return JsonResponseSuccess(data={
        "email_used": sending_email.email_address,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def faq_page(_request):
    """
    API for FAQ page.
    """
    return JsonResponseSuccess()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def manage_subscription(request):
    """
    API for Manage Workspace Subscription page.
    """
    user: User = request.user

    if request.method == "GET":
        plans: List[Dict] = []
        for plan in SubscriptionPlan.objects.filter(hidden=False):
            plans.append({
                "id": plan.id,
                "name": plan.plan_name,
                "monthly_amount": plan.monthly_amount,
                "annual_amount": plan.annual_amount,
                "popular": plan.popular,
                "monthly_feature_list": plan.monthly_feature_list,
                "annual_feature_list": plan.annual_feature_list,
                "current_plan": user.active_workspace.subscription_plan == plan,
            })

        if user.active_workspace.billing_period == "monthly":
            total_credits = user.active_workspace.subscription_plan.monthly_email_sending_quota
        else:
            total_credits = user.active_workspace.subscription_plan.annual_email_sending_quota

        return JsonResponseSuccess(data={
            "plans": plans,
            "current_plan": user.active_workspace.subscription_plan.plan_name,
            "next_limit_renewal_ts": int(user.active_workspace.next_renewal_date.timestamp() * 1000),
            "total_email_limit": total_credits,
            "remaining_email_limit": user.active_workspace.credits_remaining,
            "domain_limit": user.active_workspace.subscription_plan.total_domain_connections_allowed,
            "total_domains_connected": user.active_workspace.managedsubdomain_set.filter(setup_complete=True).count(),
            "stripe_portal_available": user.stripe_customer_id is not None,
            "current_billing_period": user.active_workspace.billing_period,
            "paid_user": not user.active_workspace.subscription_plan.is_free_plan,
        })

    else:
        return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def start_checkout_session(request):
    """
    API to start Stripe checkout session for given plan.
    """
    user: User = request.user

    try:
        plan_id: str = request.data["plan_id"]
        billing_period: str = request.data["billing_period"]
        affiliate_id: str = request.data.get("affiliate_id", None)
    except KeyError as k:
        logger.critical(f"start_checkout_session() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Fetch IP location.
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')

    try:
        country: str = get_ip_country(ip)
    except Exception as err:
        logger.critical(f"{err}")
        country = "United States"

    logger.debug(country)

    # Fetch the subscription plan.
    try:
        plan = SubscriptionPlan.objects.get(id=plan_id)
    except SubscriptionPlan.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find subscription plan on server."
        })

    # ---------------- SUPPORT FOR SIGNUP PLAN SELECTION "FREE PLAN" ----------------
    # If it's free plan, ignore request and proceed.
    # This should only happen from signup plan selection. Do not use in manage subscription code.
    if plan.is_free_plan:
        logger.debug("User selected free plan.")
        user.signup_plan_selection_done = True
        user.save()
        return JsonResponseSuccess(data={
            "is_free_plan": True
        })
    # -------------------------------------------------------------------------------

    # Fetch the price id depending on billing period.
    if billing_period == "monthly":
        price_id: str = plan.monthly_price_id
    else:
        price_id: str = plan.annual_price_id

    # Set up subscription line items.
    if country.lower() == "india":
        currency: str = "INR"
        billing_address_collection: str = "required"
        line_items: List[Dict] = [{
            "price": price_id,
            "quantity": 1,
            "tax_rates": [os.environ["CE_STRIPE_INR_TAX_RATE_ID"]]
        }]
    else:
        currency: str = "USD"
        billing_address_collection: str = "auto"
        line_items: List[Dict] = [{"price": price_id, "quantity": 1}]

    # Discounts
    if user.is_new_user_discount_eligible:
        if billing_period == "monthly":
            discounts = [{"coupon": os.environ["CE_STRIPE_NEW_USER_MONTH_COUPON_ID"]}]
        else:
            discounts = [{"coupon": os.environ["CE_STRIPE_NEW_USER_YEAR_COUPON_ID"]}]
    else:
        discounts = []

    # Cancel URL
    if user.signup_plan_selection_done:
        cancel_url = os.environ["CE_APP_HOST_URL"] + "/manage-subscription"
    else:
        cancel_url = os.environ["CE_APP_HOST_URL"] + "/onboarding/plan-selection"

    # Subscription data.
    subscription_data: Dict = {
        "metadata": {
            "workspace_id": user.active_workspace.id  # We need this to identify workspace in webhooks.
        },
    }
    if user.is_trial_period_eligible:
        subscription_data["trial_period_days"] = 7

    logger.debug(f"Starting checkout session for user {user.email} with {plan.plan_name} plan.")

    stripe_checkout_args = {
        "mode": "subscription",
        "currency": currency,
        "billing_address_collection": billing_address_collection,
        "line_items": line_items,
        "ui_mode": "hosted",
        "success_url": os.environ["CE_STRIPE_SUCCESS_URL"],
        "cancel_url": cancel_url,
        "discounts": discounts,
        "subscription_data": subscription_data,
    }

    if user.stripe_customer_id:
        stripe_checkout_args["customer"] = user.stripe_customer_id
    else:
        stripe_checkout_args["customer_email"] = user.email

    logger.info(f"Affiliate ID -> {affiliate_id}")
    if affiliate_id:
        stripe_checkout_args["client_reference_id"] = affiliate_id

    session = stripe.checkout.Session.create(**stripe_checkout_args)

    # if user.stripe_customer_id:
    #     session = stripe.checkout.Session.create(
    #         customer=user.stripe_customer_id,
    #         mode="subscription",
    #         currency=currency,
    #         billing_address_collection=billing_address_collection,
    #         line_items=line_items,
    #         ui_mode="hosted",
    #         success_url=os.environ["CE_STRIPE_SUCCESS_URL"],
    #         cancel_url=cancel_url,
    #         discounts=discounts,
    #         subscription_data=subscription_data,
    #     )
    # else:
    #     session = stripe.checkout.Session.create(
    #         customer_email=user.email,
    #         mode="subscription",
    #         currency=currency,
    #         billing_address_collection=billing_address_collection,
    #         line_items=line_items,
    #         ui_mode="hosted",
    #         success_url=os.environ["CE_STRIPE_SUCCESS_URL"],
    #         cancel_url=cancel_url,
    #         discounts=discounts,
    #         subscription_data=subscription_data,
    #     )

    return JsonResponseSuccess(data={
        "session_url": session["url"]
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def start_stripe_customer_portal_session(request):
    """
    API to start stripe customer portal session. Returns stripe portal url.
    """
    user: User = request.user

    if not user.stripe_customer_id:
        return JsonResponseBadRequest(data={
            "message": "User does not have a stripe portal."
        })

    session = stripe.billing_portal.Session.create(
        customer=user.stripe_customer_id,
        return_url=os.environ["CE_STRIPE_CUSTOMER_PORTAL_RETURN_URL"],
    )

    return JsonResponseSuccess(data={
        "url": session["url"]
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def change_subscription_plan(request):
    """
    API for plan upgrade/downgrade. Paid plans only.
    """
    user: User = request.user

    try:
        new_plan_id: str = request.data["plan_id"]
        billing_period: str = request.data["billing_period"]
    except KeyError as k:
        logger.critical(f"change_subscription_plan() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Fetch the new subscription plan.
    try:
        new_plan = SubscriptionPlan.objects.get(id=new_plan_id)
    except SubscriptionPlan.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find subscription plan on server."
        })

    # Get the subscription item id from current subscription plan
    current_sub = stripe.Subscription.retrieve(user.active_workspace.stripe_subscription_id)
    subscription_item_id: str = current_sub["items"]["data"][0]["id"]

    # Check if this is an upgrade or downgrade.
    if new_plan.plan_tier == user.active_workspace.subscription_plan.plan_tier:
        # Upgrade if monthly to annual. Downgrade otherwise.
        if (user.active_workspace.billing_period == "monthly") and (billing_period == "annual"):
            subscription = stripe.Subscription.modify(
                user.active_workspace.stripe_subscription_id,
                items=[{
                    "id": subscription_item_id,
                    "price": new_plan.annual_price_id,
                }],
                proration_behavior="always_invoice",
                billing_cycle_anchor="now",
                payment_behavior="pending_if_incomplete",
            )

        else:
            subscription = stripe.Subscription.modify(
                user.active_workspace.stripe_subscription_id,
                items=[{
                    "id": subscription_item_id,
                    "price": new_plan.monthly_price_id,
                }],
                proration_behavior="none",
                payment_behavior="pending_if_incomplete",
            )

    elif new_plan.plan_tier > user.active_workspace.subscription_plan.plan_tier:
        # Upgrade.
        subscription = stripe.Subscription.modify(
            user.active_workspace.stripe_subscription_id,
            items=[{
                "id": subscription_item_id,
                "price": new_plan.monthly_price_id if billing_period == "monthly" else new_plan.annual_price_id,
            }],
            proration_behavior="always_invoice",
            billing_cycle_anchor="now",
            payment_behavior="pending_if_incomplete",
        )

    else:
        # Downgrade.
        subscription = stripe.Subscription.modify(
            user.active_workspace.stripe_subscription_id,
            items=[{
                "id": subscription_item_id,
                "price": new_plan.monthly_price_id if billing_period == "monthly" else new_plan.annual_price_id,
            }],
            proration_behavior="none",
            payment_behavior="pending_if_incomplete",
        )

    # Get the latest invoice
    latest_invoice = stripe.Invoice.retrieve(subscription.latest_invoice, expand=[
        "confirmation_secret", "payments.data.payment.payment_intent"
    ])

    # Check for additional actions.
    if latest_invoice.payments.data[0].payment.payment_intent.status == "requires_action":
        # Return the client_secret and payment intent id to your frontend to handle authentication.
        # payment_intent_id: str = latest_invoice.payments.data[0].payment.payment_intent.id
        client_secret: str = latest_invoice.confirmation_secret.client_secret
        logger.info(f"Workspace {user.active_workspace.name} (id: {user.active_workspace.id}) requires additional "
                    f"action to complete the payment. Client Secret: {client_secret}")
        return JsonResponseSuccess(data={
            "additonal_action": True,
            "client_secret": client_secret,
            "sub_id": user.active_workspace.stripe_subscription_id,
        })
    else:
        logger.info(f"Workspace {user.active_workspace.name} (id: {user.active_workspace.id}) moved "
                    f"to {new_plan.plan_name} ({billing_period}) successfully!")
        return JsonResponseSuccess(data={
            "additonal_action": False,
        })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def archive_campaign(request):
    """
    API to archive completed or cancelled campaigns.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.data["campaign_uid"]
    except KeyError as k:
        logger.critical(f"archive_campaign() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        camp: Campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find campaign on server."
        })

    if camp.status not in ["complete", "cancelled"]:
        return JsonResponseBadRequest(data={
            "message": "Campaign cannot be archived in current state."
        })

    camp.archived = True
    camp.save()

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def campaign_export_data(request):
    user: User = request.user
    campaign_uid = request.query_params.get("campaign_uid")

    if not campaign_uid:
        return JsonResponseBadRequest(data={'success': False, 'message': "'campaign_uid' query parameter is required."})

    try:
        campaign = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseBadRequest(data={'success': False, 'message': "Campaign not found."})

    contacts = CampaignContact.objects.filter(campaign=campaign)
    schedules = CampaignSchedule.objects.filter(campaign=campaign)
    schedule_map = {s.contact_id: s for s in schedules}
    bad_emails = set(BadEmail.objects.values_list("email_id", flat=True))
    bad_reason_map = dict(BadEmail.objects.values_list("email_id", "reason"))
    unsubscribed = set(UnsubscribedEmail.objects.values_list("email_id", flat=True))

    serializer = CampaignExportContactSerializer(
        contacts,
        many=True,
        context={
            "schedule_map": schedule_map,
            "bad_emails": bad_emails,
            "bad_reason_map": bad_reason_map,
            "unsubscribed": unsubscribed
        }
    )

    return JsonResponseSuccess({'success': True, 'data': serializer.data})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_credit_history(request):
    """
    API to get credit history.
    """
    user = request.user
    workspace = user.active_workspace

    if not workspace:
        return JsonResponseBadRequest({"error": "No active workspace found for user."})

    schedules = CampaignSchedule.objects.filter(
        campaign__workspace=workspace,
        sent_on__isnull=False,
        contact__sending_email__isnull=False
    ).select_related(
        "campaign", "contact__sending_email"
    ).order_by("-sent_on")

    serializer = CreditHistorySerializer(schedules, many=True)
    return JsonResponseSuccess({'data': serializer.data, 'timezone': workspace.time_zone})


@api_view(["GET"])
@permission_classes([AllowAny])
def verify_email(request):
    """
    API to verify email.
    """
    try:
        try:
            encrypted_email: bytes = request.query_params.get('token')
            logger.debug(encrypted_email)
        except KeyError:
            return JsonResponseBadRequest()

        expiry_sec = int(os.environ.get('EXPIRY_SECONDS', 1800))
        email = decrypt_verification_token(encrypted_email, expiry_sec)
        user = User.objects.get(email=email)
        user.email_verified = True
        user.save()

        # Send email to user.
        send_email_task.delay(
            to=user.email,
            sender="<EMAIL>",
            sender_name="Junaid Ansari",
            subject=f"Welcome to DeliverymanAI, {user.username}",
            body_html=signup_onboarding_email_message(
                username=user.username,
                dashboard_url=os.environ["CE_APP_HOST_URL"] + "/dashboard",
                current_year=str(datetime.now().year),
            )
        )

        return JsonResponseSuccess()
    except Exception:
        return JsonResponseBadRequest()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def resend_verification_email(request):
    """
    Resend Verification_email
    """
    user: User = request.user
    if user.email_verified:
        return JsonResponseBadRequest({"message": "Email already verified."})

    token = generate_verification_token(user.email)
    verify_url = f"{os.environ['CE_APP_HOST_URL']}/auth/verify-account-email/{token}"

    send_email_task.delay(
        to=user.email,
        sender="<EMAIL>",
        sender_name="Junaid Ansari",
        subject=f"Verify your email to unlock DeliverymanAI",
        body_html=account_email_verification_email_body(username=user.username, verify_url=verify_url,
                                                        current_year=str(datetime.now().year))
    )

    return JsonResponseSuccess({"status": True, "message": "Verification email resent."})


@api_view(['GET'])
def get_update_post(request):
    """
    Get Update Posts.

    :param request: Django Rest Framework's Request object.
    """
    return JsonResponseSuccess({
        'data': UpdateSectionSerializer(UpdateSection.objects.all().order_by("-created_at"), many=True).data
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def contact_lists(request):
    """
    API for Contact Lists page data.
    """
    user: User = request.user

    # Fetch all imported contact lists.
    imported_contact_lists = []
    for cl in user.active_workspace.importedcontactlist_set.prefetch_related("contact_set").all():
        if cl.status == "active":
            column_count: int = len(cl.contact_set.all()[0].get_attribute_names()) + 1  # +1 for email column.
        else:
            column_count: int = 0

        imported_contact_lists.append({
            "uid": cl.uid,
            "name": cl.name,
            "created_on": int(cl.created_on.timestamp() * 1000),
            "status": cl.status,
            "contacts_count": cl.contact_set.count(),
            "column_count": column_count
        })

    logger.debug(imported_contact_lists)

    return JsonResponseSuccess(data={
        "user_id": user.id,
        "contact_lists": imported_contact_lists,
        "timezone": user.active_workspace.time_zone,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_contact_list(request):
    """
    API to create a new contact list.
    """
    user: User = request.user

    try:
        list_name: str = request.data["name"]
        csv_data: List[List[str]] = request.data["csv_data"]
        csv_headers: List[str] = request.data["csv_headers"]
        email_column_index: int = request.data["email_column_index"]
    except KeyError as k:
        logger.critical(f"add_contact_list() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Check for row and column limits.
    if (len(csv_data) > CONTACT_LIST_MAX_ROWS) or (len(csv_headers) > CONTACT_LIST_MAX_COLS):
        return JsonResponseBadRequest(data={
            "message": f"We only accept a maximum of {CONTACT_LIST_MAX_ROWS} rows and {CONTACT_LIST_MAX_COLS} columns."
        })

    # Create the contact list.
    contact_list_uid: str = f"contact_list_{uuid.uuid4().hex}"
    new_contact_list = ImportedContactList.objects.create(
        uid=contact_list_uid,
        workspace=user.active_workspace,
        name=list_name,
    )

    # Start celery task.
    logger.debug("Starting celery task 'contact_list_setup_task'...")
    contact_list_setup_task.delay(new_contact_list.id, csv_data, csv_headers, email_column_index)

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def delete_contact_list(request):
    """
    API for deleting contact list based on uid.
    """
    user: User = request.user

    try:
        contact_list_uid: str = request.data["contact_list_uid"]
    except KeyError as k:
        logger.critical(f"delete_contact_list() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        contact_list: ImportedContactList = user.active_workspace.importedcontactlist_set.prefetch_related(
            "contact_set"
        ).get(
            uid=contact_list_uid
        )
    except ImportedContactList.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Contact list not found in this workspace."
        })

    logger.debug(f"Deleting contact list {contact_list.name}...")

    contact_list.delete()

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def contact_list_details(request):
    """
    API for fetching all contacts data in a contact list for contact list details page.
    """
    user: User = request.user

    try:
        contact_list_uid: str = request.query_params["uid"]
    except KeyError as k:
        logger.critical(f"contact_list_details() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        contact_list: ImportedContactList = user.active_workspace.importedcontactlist_set.prefetch_related(
            "contact_set"
        ).get(
            uid=contact_list_uid
        )
    except ImportedContactList.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Contact list not found in this workspace."
        })

    contacts = []
    for contact in contact_list.contact_set.all():
        contacts.append({
            "uid": contact.uid,
            "email": contact.email_id,
            "bounced": contact.bounced,
            "unsubscribed": contact.unsubscribed,
            **contact.attributes,
        })

    attribute_column_headers: List[str] = contact_list.contact_set.all()[0].get_attribute_names()
    return JsonResponseSuccess(data={
        "list_name": contact_list.name,
        "contacts": contacts,
        "attribute_column_headers": attribute_column_headers,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_reply_classification(request):
    """
    API to Update reply classification for a campaign schedule.
    """
    map_reply_message = {
        "positive": "Positive",
        "negative": "Negative",
        "neutral": "Neutral",
    }
    campaign_activity_uid = request.data.get("campaignActivityUid")
    campaign_schedule_uid = request.data.get("campaignScheduleUid")
    reply_message = request.data.get("reply")

    if not campaign_activity_uid or not campaign_schedule_uid or not reply_message:
        return JsonResponseBadRequest(
            data={'message': "campaignActivityUid, campaignScheduleUid, and reply are required."})

    if reply_message not in map_reply_message:
        return JsonResponseBadRequest(data={'message': "Invalid reply classification."})

    try:
        schedule = CampaignSchedule.objects.get(uid=campaign_schedule_uid)
    except CampaignSchedule.DoesNotExist:
        return JsonResponseBadRequest(data={'message': "Schedule not found."})

    try:
        campaign_activity = CampaignActivity.objects.get(id=campaign_activity_uid)
    except CampaignActivity.DoesNotExist:
        return JsonResponseBadRequest(data={'message': "Campaign activity not found."})

    # Update reply classification on schedule
    schedule.reply_classification = reply_message
    schedule.save(update_fields=["reply_classification"])

    # Update subject in campaign activity
    campaign_activity.event_subject = f"{map_reply_message[reply_message]} Reply"
    campaign_activity.event_date = timezone.now()
    campaign_activity.save(update_fields=["event_subject", "event_date"])

    return JsonResponseSuccess({
        "uid": schedule.uid,
        "reply_classification": schedule.reply_classification
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def subscribe_and_unsubscribe(request):
    """
    API to Subscribe or Unsubscribe a user by email for a workspace.
    """
    campaign_uid = request.data.get("campaignUid")
    email_id = request.data.get("email")
    action = request.data.get("action")

    if not email_id or not action:
        return JsonResponseBadRequest(data={'message': "email, workspace_id and action are required."})

    try:
        cm = CampaignManager(campaign_uid)
    except CampaignNotFound:
        logger.error(f"campaign_unsubscribe_contact() - Could not find campaign {campaign_uid}")
        return JsonResponseBadRequest(data={"message": "No such campaign."})

    if action == "unsubscribe":
        logger.debug(f"Unsubcribing contact {email_id} from campaign {campaign_uid}...")
        try:
            cm.unsubscribe_contact(email_id=email_id)
        except CampaignContactNotFound as err:
            logger.error(f"campaign_unsubscribe_contact() - {err}")
            return JsonResponseBadRequest(data={"message": "No such contact."})
        return JsonResponseSuccess()

    elif action == "resubscribe":
        logger.debug(f"Unsubcribing contact {email_id} from campaign {campaign_uid}...")
        try:
            cm.resubscribe_contact(email_id=email_id)
        except CampaignContactNotFound as err:
            logger.error(f"campaign_unsubscribe_contact() - {err}")
            return JsonResponseBadRequest(data={"message": "No such contact."})
        return JsonResponseSuccess()
    else:
        return JsonResponseBadRequest(data={'message': "Invalid action. Use 'unsubscribe' or 'resubscribe'."})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def integration_request(request):
    """
    API to send integration request to admin.
    """
    user: User = request.user

    try:
        intreq = request.data.get("integration_request")
        integration_explaination = request.data.get("integration_explaination", "")
    except KeyError as k:
        logger.critical(f"integration_request() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    send_email_task.delay(
        to=os.environ["CE_INTEGRATION_REQUEST_EMAILS"].split(","),
        sender="<EMAIL>",
        sender_name="Deliveryman",
        subject="New Integration Request on Deliveryman.ai",
        body_html=new_integration_request_email_message(
            integration_request=intreq,
            email=user.email,
            integration_explaination=integration_explaination
        )
    )

    return JsonResponseSuccess()


@api_view(["POST"])
@permission_classes([AllowAny])
def sns_email_tracking(request):
    """
    Webhook for SNS to send various SES event data.
    """
    if "Type" in request.data:
        sns_request_type: str = request.data["Type"]
        logger.debug(sns_request_type)

        # For subscription confirmation.
        if sns_request_type == "SubscriptionConfirmation":
            res = requests.get(request.data["SubscribeURL"])
            if res.status_code != 200:
                logger.error(res.text)
                raise Exception(f"SubscriptionConfirmation SubscribeURL failed with status code - {res.status_code}")

            return JsonResponseSuccess()

        else:
            # Ignore event.
            logger.error(f"Unknown 'Type' value for sns_email_tracking - {sns_request_type}")
            return JsonResponseSuccess()

    # For tracking data.
    else:
        logger.debug(request.data)

        event_type: str = request.data["eventType"]

        if event_type == "Open":
            # Fetch the schedule. In case schedule is not found for any reason, skip the event.
            schedule_uid: str = request.data["mail"]["tags"]["schedule_uid"][0]
            try:
                schedule = CampaignSchedule.objects.get(uid=schedule_uid)
            except CampaignSchedule.DoesNotExist:
                logger.error(f"Could not find campaign schedule with uid {schedule_uid}. Skipping.")
                return JsonResponseSuccess()

            open_data: Dict = request.data["open"]
            open_datetime = datetime.fromisoformat(open_data["timestamp"])

            schedule.email_opened = True
            schedule.email_opened_on = open_datetime
            schedule.save()

            logger.debug(f"Email opened for schedule {schedule_uid}")

        elif event_type == "Click":
            # Fetch the schedule. In case schedule is not found for any reason, skip the event.
            schedule_uid: str = request.data["mail"]["tags"]["schedule_uid"][0]
            try:
                schedule = CampaignSchedule.objects.get(uid=schedule_uid)
            except CampaignSchedule.DoesNotExist:
                logger.error(f"Could not find campaign schedule with uid {schedule_uid}. Skipping.")
                return JsonResponseSuccess()

            click_data: Dict = request.data["click"]
            click_datetime = datetime.fromisoformat(click_data["timestamp"])
            link: str = click_data["link"]

            EmailLinkClickEvent.objects.create(
                schedule=schedule,
                link=link,
                clicked_on=click_datetime,
            )

            logger.debug(f"Link clicked for schedule {schedule_uid}")

        else:
            logger.warning(f"Unhandled event type for sns_email_tracking - '{event_type}'")

        return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_campaign_open_events(request):
    """
    API to fetch email open events for given campaign.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"get_campaign_open_event() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        camp = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find campaign on server."
        })

    events: List[Dict] = []
    for schedule in camp.campaignschedule_set.filter(email_opened=True).order_by("-email_opened_on"):
        events.append({
            "id": schedule.id,
            "contact_email": schedule.contact.email_id,
            "open_date": schedule.email_opened_on,
        })

    return JsonResponseSuccess(data={
        "events": events,
        "timezone": user.active_workspace.time_zone,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_link_click_events(request):
    """
    API to fetch link click events data for given campaign.
    """
    user: User = request.user

    try:
        campaign_uid: str = request.query_params["campaign_uid"]
    except KeyError as k:
        logger.critical(f"get_link_click_events() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        camp = user.active_workspace.campaign_set.get(uid=campaign_uid)
    except Campaign.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": "Could not find campaign on server."
        })

    event_id: int = 0
    events: List[Dict] = []
    for click_event in EmailLinkClickEvent.objects.filter(
            schedule__campaign__id=camp.id
    ).values(
        "link", "schedule__contact__email_id"
    ).annotate(
        clicks=Count("link"), latest_date=Max("clicked_on")
    ).order_by("-latest_date"):
        events.append({
            "id": event_id,
            "contact_email": click_event["schedule__contact__email_id"],
            "link": click_event["link"],
            "clicks": click_event["clicks"],
            "latest_date": click_event["latest_date"],
        })
        event_id += 1

    return JsonResponseSuccess(data={
        "events": events,
        "timezone": user.active_workspace.time_zone,
    })
