import html
import logging
import time
from typing import List, Dict

import dns.resolver
from celery.result import AsyncR<PERSON>ult
from dateutil.relativedelta import relativedelta
from django.db import IntegrityError
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from ColdEmailerBackend.settings import aws_ses, aws_route53, DEBUG, INITIAL_WARMUP_EMAIL_COUNT, \
    INITIAL_DAILY_EMAIL_LIMIT
from mainapp.aws import create_hosted_zone, get_hosted_zone_nameservers
from mainapp.models import User, ManagedSubdomain, EmailID
from mainapp.pydantic_models import GooglePostmasterDomainSchema
from mainapp.responses import JsonResponseBadRequest, JsonResponseSuccess, JsonResponseNotFound, JsonResponseServerError
from mainapp.route53 import delete_route53_records
from mainapp.tasks import naming_strategy_stage_task, email_setup_task
from mainapp.utils import is_valid_domain

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_managed_subdomain(request):
    """
    API to create a new managed subdomain for an account. This adds the subdomain to AWS Route53 and returns the
    nameservers that need to be added as NS records.
    """
    user: User = request.user

    try:
        fqdn: str = html.escape(request.data["fqdn"].lower().strip())
        next_stage_number: int = request.data["next_stage_number"]
    except KeyError as k:
        logger.critical(f"add_domain() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Remove http from domain.
    if fqdn.startswith("http://"):
        fqdn = fqdn.replace("http://", "")

    # Remove https from domain.
    if fqdn.startswith("https://"):
        fqdn = fqdn.replace("https://", "")

    # Remove www from domain.
    if fqdn.startswith("www."):
        fqdn = fqdn.replace("www.", "")

    # Check for validity.
    if not is_valid_domain(fqdn):
        return JsonResponseBadRequest(data={
            "message": "Please provide a valid domain (ex: example.com, sub.example.com)"
        })

    try:
        # We'll initiall keep usable date 5 years in future until user fully completes the setup process.
        managed_subdomain = ManagedSubdomain.objects.create(
            workspace=user.active_workspace,
            subdomain=fqdn,
            warmup_email_count=INITIAL_WARMUP_EMAIL_COUNT,
            email_limit=INITIAL_DAILY_EMAIL_LIMIT,
            domain_usable_from=timezone.now() + relativedelta(years=5),
            status="warmup",
        )
    except IntegrityError:
        logger.debug("add_managed_subdomain() - Unable to add. This subdomain has already been connected.")
        return JsonResponseBadRequest(data={
            "message": "Unable to add. This subdomain has already been connected."
        })

    # Add subdomain on AWS Route53
    hosted_zone_id: str = create_hosted_zone(fqdn=fqdn, user_email=user.email)
    managed_subdomain.hosted_zone_id = hosted_zone_id

    # Get the required nameservers.
    nameservers: List[str] = get_hosted_zone_nameservers(hosted_zone_id)

    # Move to next stage.
    managed_subdomain.current_setup_stage = next_stage_number
    managed_subdomain.save()

    return JsonResponseSuccess(data={
        "nameservers": nameservers,
        "subdomain": fqdn
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def check_nameserver_records(request):
    """
    API which checks if nameserver for a managed subdomain has been applied correctly.
    Returns dictionary with "success" as boolean value.

    We won't set next stage number for this stage.
    """
    user: User = request.user

    try:
        subdomain: str = html.escape(request.query_params["subdomain"].lower().strip())
    except KeyError as k:
        logger.critical(f"check_nameservers() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"check_nameservers() - Could not find managed subdomain {subdomain} for {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    # Get the required nameservers.
    try:
        required_nameservers: List[str] = get_hosted_zone_nameservers(managed_subdomain.hosted_zone_id)
    except Exception as err:
        logger.error(f"check_nameservers() - {err}")
        return JsonResponseBadRequest(data={
            "message": "Nameservers have not been generated for this subdomain."
        })

    # Fetch the current NS records for subdomain using pythondns
    try:
        resolver = dns.resolver.Resolver()
        resolver.nameservers = ['*******', '*******']
        resolver.cache = None
        dns_result: dns.resolver.Answer = resolver.resolve(subdomain, "NS")
    except dns.resolver.NoAnswer:
        logger.debug(f"check_nameservers() - No NS record results for {subdomain}")
        return JsonResponseSuccess(data={
            "success": False
        })
    except dns.resolver.NoNameservers:
        logger.debug(f"check_nameservers() - No NS record results for {subdomain}")
        return JsonResponseSuccess(data={
            "success": False
        })

    # noinspection PyUnresolvedReferences
    current_nameservers: List[str] = [ns.target.to_text().strip(".") for ns in dns_result]

    success: bool = all([ns in current_nameservers for ns in required_nameservers])
    if success:
        managed_subdomain.save()

    # If all the required NS records are present in current nameservers, send "success" as True.
    return JsonResponseSuccess(data={
        "success": success
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def save_naming_strategy_and_contact_count(request):
    """
    API to save naming strategy and contact count in connect domain wizard.
    """
    user: User = request.user

    try:
        subdomain: str = html.escape(request.data["subdomain"].lower().strip())
        naming_strategy: str = html.escape(request.data["naming_strategy"].lower().strip())
        custom_name: str = request.data["custom_name"]  # Will be None if naming_strategy is not "custom".
        contacts_count: int = 2000
        next_stage_number: int = request.data["next_stage_number"]
        country: str = request.data["country"]
    except KeyError as k:
        logger.critical(f"save_naming_strategy() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    if naming_strategy not in [strat[0] for strat in ManagedSubdomain.NAMING_STRATEGIES]:
        logger.critical(f"save_naming_strategy() - Invalid naming strategy {naming_strategy}.")
        return JsonResponseBadRequest(data={
            "message": f"Invalid naming strategy {naming_strategy}."
        })

    if (naming_strategy == "custom") and (not custom_name):
        logger.critical(f"save_naming_strategy() - Missing custom name.")
        return JsonResponseBadRequest(data={
            "message": f"Missing custom name."
        })

    if custom_name is not None:
        custom_name = html.escape(custom_name.lower().strip())
        custom_name_parts = custom_name.split(" ")
        for part in custom_name_parts:
            if len(part) <= 1:
                return JsonResponseBadRequest(data={
                    "message": f"Firstame/lastname should have minimum of 2 characters to meet our email id generation requirements."
                })

        if custom_name_parts[0][0:2] == custom_name_parts[1][0:2]:
            return JsonResponseBadRequest(data={
                "message": "First 2 characters of firstname and lastname cannot be same to meet our email id generation requirements."
            })

    try:
        managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"Could not find managed subdomain {subdomain} in account {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    logger.debug(f"Selected country -> {country}")

    result = naming_strategy_stage_task.delay(user.id, subdomain, contacts_count, naming_strategy, custom_name, country,
                                              next_stage_number)
    managed_subdomain.celery_naming_strategy_task_id = result.id
    managed_subdomain.save()

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def check_email_preview_generation_done(request):
    """
    Lets you know if naming_strategy_stage_task for given subdomain is done or not.
    """
    user: User = request.user

    try:
        subdomain: str = html.escape(request.query_params["subdomain"].lower().strip())
    except KeyError as k:
        logger.critical(f"get_email_preview() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    result = AsyncResult(managed_subdomain.celery_naming_strategy_task_id)
    if result.status == "SUCCESS":
        success: bool = result.get()["success"]
        if success:
            return JsonResponseSuccess(data={
                "success": True
            })
        else:
            return JsonResponseServerError(data={
                "message": f"Celery task {managed_subdomain.celery_naming_strategy_task_id} failed."
            })

    elif result.status == "FAILURE":
        return JsonResponseServerError(data={
            "message": f"Celery task {managed_subdomain.celery_naming_strategy_task_id} ran into some error."
        })

    else:
        return JsonResponseSuccess(data={
            "success": False
        })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_email_preview(request):
    """
    API that returns email preview data for connect subdomain wizard.
    """
    user: User = request.user

    try:
        subdomain: str = html.escape(request.query_params["subdomain"].lower().strip())
    except KeyError as k:
        logger.critical(f"get_email_preview() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    email_preview_data: List[Dict] = []
    for e_sub in managed_subdomain.emailsubdomain_set.all():
        for email in e_sub.emailid_set.all():
            email_preview_data.append({
                "id": email.id,
                "email": email.email_address,
                "subdomain": e_sub.subdomain,
            })

    return JsonResponseSuccess(data={
        "preview": email_preview_data,
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def setup_email_aliases(request):
    """
    API to set up email aliases.
    """
    user: User = request.user

    try:
        subdomain: str = html.escape(request.data["subdomain"].lower().strip())
        next_stage_number: int = request.data["next_stage_number"]
    except KeyError as k:
        logger.critical(f"setup_email_aliases() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"check_nameservers() - Could not find managed subdomain {subdomain} for {user.email}")
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    result = email_setup_task.delay(subdomain, user.id, next_stage_number)

    managed_subdomain.celery_setup_emails_task_id = result.id
    managed_subdomain.save()

    return JsonResponseSuccess()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def check_email_setup_done(request):
    """
    Lets you know if email_setup_task for given subdomain is done or not.
    """
    user: User = request.user

    try:
        subdomain: str = html.escape(request.query_params["subdomain"].lower().strip())
    except KeyError as k:
        logger.critical(f"get_email_preview() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        return JsonResponseNotFound(data={
            "message": f"Could not find resource on server."
        })

    result = AsyncResult(managed_subdomain.celery_setup_emails_task_id)
    if result.status == "SUCCESS":
        success: bool = result.get()["success"]
        if success:
            return JsonResponseSuccess(data={
                "success": True
            })
        else:
            return JsonResponseServerError(data={
                "message": f"Celery task {managed_subdomain.celery_setup_emails_task_id} failed."
            })

    elif result.status == "FAILURE":
        return JsonResponseServerError(data={
            "message": f"Celery task {managed_subdomain.celery_setup_emails_task_id} ran into some error."
        })

    else:
        return JsonResponseSuccess(data={
            "success": False
        })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def remove_managed_subdomain(request):
    """
    API to remove (delete) a managed subdomain from user account.
    """
    user: User = request.user

    try:
        subdomain: str = request.data["subdomain"]
    except KeyError as k:
        logger.critical(f"remove_managed_subdomain() - Missing key {k}")
        return JsonResponseBadRequest(data={"message": f"Missing key {k}"})

    try:
        msub: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"remove_managed_subdomain() - Could not find managed subdomain {subdomain} in "
                     f"account {user.email}")
        return JsonResponseNotFound(data={"message": "Could not find resource on server."})

    # Check if there are any running campaigns. If there are, user needs to stop them first before proceeding.
    if user.active_workspace.campaign_set.filter(
            sending_domains__in=[msub],
            status__in=["running", "paused", "scheduled"]
    ).count() > 0:
        return JsonResponseBadRequest(data={
            "message": 'Failed: One or more campaigns using this domain is in "scheduled", "running" or "paused" states.'
        })

    # Remove managed subdomain from SES Identity.
    try:
        aws_ses.delete_email_identity(EmailIdentity=subdomain)
    except aws_ses.exceptions.NotFoundException:
        logger.warning(f"remove_managed_subdomain() - SES Identity {subdomain} not found. Skipping.")

    # Remove email subdomain from SES Identity.
    for esub in msub.emailsubdomain_set.all():
        try:
            aws_ses.delete_email_identity(EmailIdentity=esub.subdomain)
        except aws_ses.exceptions.NotFoundException:
            logger.warning(f"remove_managed_subdomain() - SES Identity {msub.subdomain} not found. Skipping.")

    # Delete Route53 hosted zone. We need to remove the records first, then delete the zone.
    if msub.hosted_zone_id:
        while True:
            # Get all records in zone except for default NS and SOA.
            result = aws_route53.list_resource_record_sets(
                HostedZoneId=msub.hosted_zone_id
            )

            records: List[Dict] = result["ResourceRecordSets"]
            filtered_records: List[Dict] = list(filter(lambda x: x["Type"] not in ["NS", "SOA"], records))

            if filtered_records:
                # Delete the records.
                delete_route53_records(msub.hosted_zone_id, filtered_records)

                # If there are no more records, break.
                if not result["IsTruncated"]:
                    break

                # Wait for 6 seconds before fetching the next batch, so we don't hit rate limit.
                time.sleep(6)

            else:
                # Break if there are no more records (just an alternative if 'IsTruncated' check doesn't work).
                break

        # Delete the zone.
        aws_route53.delete_hosted_zone(
            Id=msub.hosted_zone_id
        )

        # Remove google postmaster integration for this domain/subdomain.
        try:
            if user.google_postmaster_integration:
                current_domain_list: List[GooglePostmasterDomainSchema] = [
                    GooglePostmasterDomainSchema(**item) for item in user.google_postmaster_integration.domains_added
                ]

                updated_domain_list: List[GooglePostmasterDomainSchema] = list(
                    filter(lambda x: x.domain != subdomain, current_domain_list)
                )

                user.google_postmaster_integration.domains_added = [item.model_dump() for item in updated_domain_list]
                user.google_postmaster_integration.save()

        except Exception as err:
            logger.critical(err, exc_info=True)

    else:
        logger.warning(f"remove_managed_subdomain() - Failed to delete Route53 hosted zone for "
                       f"managed subdomain {msub.subdomain}. Missing 'hosted_zone_id' value. Skipping.")

    # Delete database entries.
    msub.delete()

    return JsonResponseSuccess(data={
        "subdomain": subdomain,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def success_stage_data(request):
    """
    API for details on domain connection success page.
    """
    user: User = request.user

    try:
        domain: str = request.query_params["domain"]
    except KeyError as k:
        logger.critical(f"success_stage_data() - Missing key {k}")
        return JsonResponseSuccess(data={
            "message": f"Missing key {k}"
        })

    try:
        msub = user.active_workspace.managedsubdomain_set.get(subdomain=domain)
    except ManagedSubdomain.DoesNotExist:
        return JsonResponseSuccess(data={
            "message": "Could not find domain data on server."
        })

    return JsonResponseSuccess(data={
        "domain": msub.subdomain,
        "total_emails": EmailID.objects.filter(email_subdomain__managed_subdomain=msub).count(),
        "domain_usable_from_ts": int(msub.domain_usable_from.timestamp() * 1000)
    })
