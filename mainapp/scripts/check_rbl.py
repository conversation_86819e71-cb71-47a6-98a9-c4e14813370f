import socket
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List


class CheckDNSBLDomain:
    def __init__(self):
        self._dnsbl_servers = [
            "sbl.spamhaus.org",         # Spamhaus SBL
            "css.spamhaus.org",         # Spamhaus CSS
            "xbl.spamhaus.org",         # Spamhaus XBL
            "pbl.spamhaus.org",         # Spamhaus PBL
            "zen.spamhaus.org",         # Spamhaus ZEN (combined)
            "b.barracudacentral.org",
            "ips.backscatterer.org",
            "cdl.anti-spam.org.cn",
            "ubl.lashback.com",
            "bl.spamcop.net",
            "backscatter.spameatingmonkey.net",
            "uribl.spameatingmonkey.net",
            "urired.spameatingmonkey.net",
            "psbl.surriel.com",
            "rbl.swinog.ch",
            "truncate.gbudb.net",
            "bl.blocklist.de",
            "bl.spameatingmonkey.net",
            "blackholes.five-ten-sg.com",
            "bogons.cymru.com",
            "cbl.abuseat.org",
            "combined.abuse.ch",
            "db.wpbl.info",
            "dnsbl-1.uceprotect.net",
            "dnsbl-2.uceprotect.net",
            "dnsbl-3.uceprotect.net",
            "dnsbl.dronebl.org",
            "dnsbl.inps.de",
            "drone.abuse.ch",
            "dul.ru",
            "dyna.spamrats.com",
            "no-more-funn.moensted.dk",
            "noptr.spamrats.com",
            "ohps.dnsbl.net.au",
            "omrs.dnsbl.net.au",
            "orvedb.aupads.org",
            "osps.dnsbl.net.au",
            "osrs.dnsbl.net.au",
            "owfs.dnsbl.net.au",
            "owps.dnsbl.net.au",
            "phishing.rbl.msrbl.net",
            "probes.dnsbl.net.au",
            "proxy.bl.gweep.ca",
            "proxy.block.transip.nl",
            "psbl.surriel.com",
            "rbl.abuse.ro",
            "rbl.interserver.net",
            "rbl.orbitrbl.com",
            "rbl.realtimeblacklist.com",
            "rbl.schulte.org",
            "rdts.dnsbl.net.au",
            "relays.bl.gweep.ca",
            "relays.bl.kundenserver.de",
            "relays.nether.net",
            "residential.block.transip.nl",
            "ricn.dnsbl.net.au",
            "rmst.dnsbl.net.au",
            "short.rbl.jp",
            "singular.ttk.pte.hu",
            "spam.abuse.ch",
            "spam.dnsbl.sorbs.net",
            "spam.rbl.msrbl.net",
            "spam.spamrats.com",
            "spamguard.leadmon.net",
            "spamlist.or.kr",
            "spamrbl.imp.ch",
            "spamsources.fabel.dk",
            "spamtrap.drbl.drand.net",
            "srnblack.surgate.net",
            "t3direct.dnsbl.net.au",
            "tor.dnsbl.sectoor.de",
            "torserver.tor.dnsbl.sectoor.de",
            "truncate.gbudb.net",
            "ubl.lashback.com",
            "ubl.unsubscore.com",
            "virbl.dnsbl.bit.nl",
            "virus.rbl.jp",
            "virus.rbl.msrbl.net",
            "wormrbl.imp.ch",
            "zen.spamhaus.org",
            "exploit.mail.abusix.zone",
            "dnsbl.anonmails.de",
            "ivmuri.abuse.ch",
            "backscatter.spameatingmonkey.net",  # SEM BACKSCATTER
            "badconf.rhsbl.sorbs.net",  # SORBS RHSBL BADCONF
            "bl.nordspam.com",  # Nordspam BL
            "bl.nosolicitado.org",  # NoSolicitado
            "black.mail.abusix.zone",  # Abusix Mail Intelligence Blacklist
            "dbl.nordspam.com",  # Nordspam DBL
            "dbl.spamhaus.org",  # Spamhaus DBL
            "dnsbl.konstant.no",  # Konstant
            "dnsbl.spfbl.net",  # SPFBL DNSBL
            "dnsbl.suomispam.net",  # Suomispam Reputation
            "domain.mail.abusix.zone",  # Abusix Domain Blacklist
            "drmx.ascams.com",  # DRMX
            "hil2.rbl.hermes-solutions.ca",  # HIL2
            "ivmsip.abuse.ch",  # ivmSIP
            "ivmsip24.abuse.ch",  # ivmSIP24
            "multi.surbl.org",  # SURBL multi
            "nomail.rhsbl.sorbs.net",  # SORBS RHSBL NOMAIL
            "rbl.0spam.org",  # 0SPAM RBL
            "rbl.swinog.ch",  # SWINOG
            "rbl.triumf.ca",  # TRIUMF
            "reputation.mail.trustedsource.org",  # Sender Score Reputation Network
            "unsure.nether.net",  # NETHERUNSURE
            "uribl.spameatingmonkey.net",  # SEM URI
            "urired.spameatingmonkey.net",  # SEM URIRED
            "z.mailspike.net"]

    @staticmethod
    def _check_single_dnsbl(domain, dnsbl, timeout=5):
        """Check a single DNSBL"""
        try:
            socket.setdefaulttimeout(timeout)
            query_domain = f"{domain}.{dnsbl}"
            result = socket.gethostbyname(query_domain)
            
            if result == '***************':
                return dnsbl, False, None, f"ERROR: {str(e)}" 
            return dnsbl, True, result, 'BLACKLISTED'
        except socket.gaierror:
            return dnsbl, False, None, 'CLEAN'
        except Exception as e:
            return dnsbl, None, None, f'ERROR: {str(e)}'

    def check_domain_in_dnsbl_threaded(self, domain, max_workers=100):
        """Check domain in multiple DNSBLs concurrently"""
        listed_in: List[str] = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_dnsbl = {
                executor.submit(self._check_single_dnsbl, domain, dnsbl): dnsbl
                for dnsbl in self._dnsbl_servers
            }

            # Process results as they complete
            for future in as_completed(future_to_dnsbl):
                dnsbl, listed, response, status = future.result()
                if listed:
                    listed_in.append(dnsbl)

        return {
            "blacklisted": len(listed_in) > 0,
            "listed_in": listed_in,
        }


if __name__ == '__main__':
    # Usage
    checker = CheckDNSBLDomain()
    dnsbl_check_result = checker.check_domain_in_dnsbl_threaded("draftss.com")
    print(dnsbl_check_result)