import base64
import datetime
import html
import json
import logging
import os
import re
import time
import uuid
from datetime import date
from datetime import timedelta
from typing import List, Dict, Literal, Optional

import whois
from celery import shared_task
from dateutil.relativedelta import relativedelta
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import IntegrityError
from django.utils import timezone
from django.utils.html import escape

from ColdEmailerBackend.settings import aws_ses, openai_client, aws_route53, aws_eventbridge
from mainapp.campaign import CampaignManager
from mainapp.email_messages import plan_renewal_paused_campaigns_email_message, domain_connected_email_message, \
    campaign_start_email_message, domain_blacklisted_removed_email_message, domain_blacklisted_email_message
from mainapp.models import EmailSubdomain, ManagedSubdomain, User, EmailID, CampaignSchedule, Campaign, \
    CampaignIgnoredContacts, CampaignContact, CampaignEmailMessage, Workspace, BadEmail, CampaignActivity, \
    UnsubscribedEmail, BlacklistCheckStatus, Blacklist<PERSON>lertStatus, Contact, ImportedContactList
from mainapp.pydantic_models import Email<PERSON>eneration, GooglePostmasterDomainSchema
from mainapp.route53 import add_route53_record, delete_route53_records
from mainapp.scripts.check_rbl import CheckDNSBLDomain
from mainapp.utils import build_email_subdomains, extract_message_id, classify_email_reply, \
    send_websocket_event, create_warmup_schedules, csv_rows_to_dict, send_email, remove_duplicate_rows

logger = logging.getLogger('coldemailer.celery_tasks')


@shared_task
def celery_test_add(x, y):
    return x + y


@shared_task
def naming_strategy_stage_task(user_id: int, subdomain: str, contacts_count: int, naming_strategy: str,
                               custom_name: str | None, country: str, next_stage_number: int, workspace_id: int | None = None):
    """
    This task does the following:
    1. Generate required email subdomains from contacts count.
    2. Set up these subdomains on SES Identity.
    3. Generate and save email ids (aliases) for each subdomain using selected naming strategy.

    :param user_id: User model id of logged in user.
    :param subdomain: Managed subdomain (ex. hello.draftss.com)
    :param contacts_count: Max number of contacts to consider while generating email subdomains.
    :param naming_strategy: Naming strategy to use during email id generation.
    :param custom_name: Name to use for "custom" naming strategy. Pass None if naming strategy is not "custom".
    :param country: Country for name generation.
    :param next_stage_number: Next wizard stage number.
    """
    user = User.objects.get(id=user_id)

    try:
        if workspace_id:
            workspace: Workspace = Workspace.objects.get(id=workspace_id)
            managed_subdomain: ManagedSubdomain = workspace.managedsubdomain_set.get(subdomain=subdomain)
        else:
            managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"Could not find managed subdomain {subdomain} in account " f"{workspace.user.email if workspace_id else user.email}")
        return {
            "subdomain": subdomain,
            "success": False,
            "reason": f"Could not find managed subdomain {subdomain} in account " f"{workspace.user.email if workspace_id else user.email}"
        }

    managed_subdomain.naming_strategy = naming_strategy
    managed_subdomain.contacts_count = contacts_count
    if naming_strategy == "custom":
        managed_subdomain.custom_name = custom_name

    # Generate the email subdomains.
    # total_subdomains_required: int = math.ceil(contacts_count / 100)
    total_subdomains_required: int = 3

    with open("mainapp/subdomain_names.txt", "r") as f:
        subdomain_names: List[str] = f.read().splitlines()

    email_subdomains: List[str] = build_email_subdomains(
        fqdn=subdomain,
        amount=total_subdomains_required,
        names_list=subdomain_names,
    )

    # Add managed domain to SES Identity.
    logger.debug(f"Setting up identity on AWS SES for {managed_subdomain.subdomain}...")
    try:
        response = aws_ses.create_email_identity(
            EmailIdentity=managed_subdomain.subdomain,
        )

        # Fetch and save the DKIM tokens.
        dkim_tokens: List[str] = response["DkimAttributes"]["Tokens"]
        managed_subdomain.dkim_tokens = dkim_tokens
        managed_subdomain.save()

        # Create CNAME records using DKIM tokens.
        logger.debug(f"Adding CNAME records for {managed_subdomain.subdomain}...")
        for token in dkim_tokens:
            record_name = f"{token}._domainkey.{managed_subdomain.subdomain}"
            record_value = f"{token}.dkim.amazonses.com"
            try:
                add_route53_record(
                    hosted_zone_id=managed_subdomain.hosted_zone_id,
                    record_type="CNAME",
                    record_name=record_name,
                    record_value=record_value,
                )
            except Exception as err:
                logger.error(err, exc_info=True)
                return {
                    "subdomain": subdomain,
                    "success": False,
                    "reason": f"Failed to add CNAME record for {managed_subdomain.subdomain}"
                }

    except aws_ses.exceptions.AlreadyExistsException:
        logger.error("This identity has already been created on SES.")

    # Go through each subdomain that was generated and set them up on SES.
    # We are not using bulk create here because we want the subdomains to be created in order.
    for email_subdomain in email_subdomains:
        new_sub = EmailSubdomain.objects.create(
            managed_subdomain=managed_subdomain,
            subdomain=email_subdomain,
        )

        # Create the SES email identity.
        logger.debug(f"Setting up identity on AWS SES for {email_subdomain}...")
        try:
            response = aws_ses.create_email_identity(
                EmailIdentity=email_subdomain,
            )
        except aws_ses.exceptions.AlreadyExistsException:
            logger.error("This identity has already been created on SES.")
            continue

        # Fetch and save the DKIM tokens.
        dkim_tokens: List[str] = response["DkimAttributes"]["Tokens"]
        new_sub.dkim_tokens = dkim_tokens
        new_sub.save()

        # Create CNAME records using DKIM tokens.
        logger.debug(f"Adding CNAME records for {email_subdomain}...")
        for token in dkim_tokens:
            record_name = f"{token}._domainkey.{email_subdomain}"
            record_value = f"{token}.dkim.amazonses.com"
            try:
                add_route53_record(
                    hosted_zone_id=managed_subdomain.hosted_zone_id,
                    record_type="CNAME",
                    record_name=record_name,
                    record_value=record_value,
                )
            except Exception as err:
                logger.error(err, exc_info=True)
                return {
                    "subdomain": subdomain,
                    "success": False,
                    "reason": f"Failed to add CNAME record for {email_subdomain}"
                }

        # Create the DMARC TXT record.
        try:
            add_route53_record(
                hosted_zone_id=managed_subdomain.hosted_zone_id,
                record_type="TXT",
                record_name=f"_DMARC.{email_subdomain}",
                record_value="\"v=DMARC1; p=none; pct=100; rua=mailto:<EMAIL>; sp=none; aspf=r;\"",
            )
        except Exception as err:
            logger.error(err, exc_info=True)
            return {
                "subdomain": subdomain,
                "success": False,
                "reason": f"Failed to add DMARC TXT record for {email_subdomain}"
            }

        # Enable MAIL FROM.
        logger.debug(f"Setting up MAIL FROM for {email_subdomain}...")
        aws_ses.put_email_identity_mail_from_attributes(
            EmailIdentity=email_subdomain,
            MailFromDomain=f"dm.{email_subdomain}",
            BehaviorOnMxFailure="REJECT_MESSAGE",
        )

        # Add MX and TXT records for MAIL FROM feature.
        logger.debug(f"[*] Adding MAIL FROM MX and TXT records for {email_subdomain}...")
        # ------ MX Record ------
        try:
            add_route53_record(
                hosted_zone_id=managed_subdomain.hosted_zone_id,
                record_type="MX",
                record_name=f"dm.{email_subdomain}",
                record_value=f"10 feedback-smtp.{os.environ['CE_AWS_REGION']}.amazonses.com",
            )
        except Exception as err:
            logger.error(err, exc_info=True)
            return {
                "subdomain": subdomain,
                "success": False,
                "reason": f"Failed to add MAIL FROM MX record for {email_subdomain}"
            }
        # ------ TXT (SPF Record) ------
        try:
            add_route53_record(
                hosted_zone_id=managed_subdomain.hosted_zone_id,
                record_type="TXT",
                record_name=f"dm.{email_subdomain}",
                record_value="\"v=spf1 include:amazonses.com ~all\"",
            )
        except Exception as err:
            logger.error(err, exc_info=True)
            return {
                "subdomain": subdomain,
                "success": False,
                "reason": f"Failed to add MAIL FROM TXT record for {email_subdomain}"
            }

        new_sub.created = True
        new_sub.save()

    logger.info(f"SES Identity setup complete for {subdomain}")

    # Generate and save the email ids.
    # We need to generate 10 emails per domain.
    logger.info(f"Generating emails for {subdomain}...")

    if managed_subdomain.naming_strategy == "custom":
        firstname: str = custom_name.split(" ")[0]
        lastname: str = custom_name.split(" ")[-1]
        email_usernames: List[str] = [
            firstname + lastname,
            firstname + lastname[0],
            firstname + "." + lastname,
            firstname + "." + lastname[0],
            firstname + "_" + lastname,
            firstname + "_" + lastname[0],
            firstname[0] + lastname,
            firstname[0] + "." + lastname,
            firstname[0] + "_" + lastname,
            firstname[0] + lastname[0] + lastname[1],
            lastname + firstname,
            lastname + firstname[0],
            lastname + "." + firstname,
            lastname + "." + firstname[0],
            lastname + "_" + firstname,
            lastname + "_" + firstname[0],
            lastname[0] + firstname,
            lastname[0] + "." + firstname,
            lastname[0] + "_" + firstname,
            lastname[0] + firstname[0] + firstname[1],
        ]

        fullname: str = " ".join([part.capitalize() for part in custom_name.split(" ")])
        for item in managed_subdomain.emailsubdomain_set.all():
            batch = []
            for username in email_usernames:
                batch.append(EmailID(
                    email_subdomain=item,
                    email_address=f"{username}@{item.subdomain}",
                    username=fullname,
                ))

            EmailID.objects.bulk_create(batch)

    else:
        if managed_subdomain.naming_strategy == "random":
            generation_message: str = (
                f"Generate 20 unique email id usernames using random male and female names from {country}. "
                f"Only use english alphabets. Do not use or add any Numbers and Special "
                f"Characters. You can use \".\" & \"_\". Do not generate duplicate usernames.")
        elif managed_subdomain.naming_strategy == "male":
            generation_message: str = (f"Generate 20 unique email id usernames using real male names from {country}. "
                                       f"Only use english alphabets. Do not use or add any Numbers and Special "
                                       f"Characters. You can use \".\" & \"_\". Do not generate duplicate usernames.")
        elif managed_subdomain.naming_strategy == "female":
            generation_message: str = (f"Generate 20 unique email id usernames using real female names from {country}. "
                                       f"Only use english alphabets. Do not use or add any Numbers and Special "
                                       f"Characters. You can use \".\" & \"_\". Do not generate duplicate usernames.")
        else:
            raise Exception(f"Unknown naming strategy '{managed_subdomain.naming_strategy}'")

        chat_completion = openai_client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "user",
                    "content": generation_message,
                }
            ],
            response_format=EmailGeneration,
        )
        email_usernames: List[str] = json.loads(chat_completion.choices[0].message.content)["usernames"]

        for item in managed_subdomain.emailsubdomain_set.all():
            batch = []
            for username in email_usernames:
                fullname: str = re.sub(r'[^a-zA-Z0-9]', ' ', username)
                # Capitalize firstname lastname
                fullname = " ".join([part.capitalize() for part in fullname.split(" ")])
                batch.append(EmailID(
                    email_subdomain=item,
                    email_address=f"{username}@{item.subdomain}",
                    username=fullname,
                ))

            EmailID.objects.bulk_create(batch)

    managed_subdomain.current_setup_stage = next_stage_number
    managed_subdomain.save()

    return {"subdomain": subdomain, "success": True}


@shared_task
def email_setup_task(subdomain: str, user_id: int, next_stage_number: int, workspace_id: int | None = None):
    """
    This task does the following:
    1. Add AWS inbound mailserver MX record to all email subdomains.
    2. Mark the email id as active.

    :param user_id: User model id of logged in user.
    :param subdomain: Managed subdomain (ex. hello.draftss.com)
    :param next_stage_number: Next wizard stage number.
    """
    user = User.objects.get(id=user_id)

    try:
        if workspace_id:
            workspace: Workspace = Workspace.objects.get(id=workspace_id)
            managed_subdomain: ManagedSubdomain = workspace.managedsubdomain_set.get(subdomain=subdomain)
        else:
            managed_subdomain: ManagedSubdomain = user.active_workspace.managedsubdomain_set.get(subdomain=subdomain)
    except ManagedSubdomain.DoesNotExist:
        logger.error(f"Could not find managed subdomain {subdomain} in account " f"{workspace.user.email if workspace_id else user.email}")
        return {
            "subdomain": subdomain,
            "success": False,
            "reason": f"Could not find managed subdomain {subdomain} in account " f"{workspace.user.email if workspace_id else user.email}"
        }

    # For each subdomain add MX record pointing to aws ses inbound mailserver.
    for email_subdomain in managed_subdomain.emailsubdomain_set.all():
        try:
            add_route53_record(
                hosted_zone_id=managed_subdomain.hosted_zone_id,
                record_type="MX",
                record_name=email_subdomain.subdomain,
                record_value=f"10 inbound-smtp.{os.environ['CE_AWS_REGION']}.amazonaws.com",
            )
        except Exception as err:
            logger.error(err, exc_info=True)
            logger.error(f"Failed to add incoming mail MX record for {email_subdomain.subdomain}")
            return {
                "subdomain": subdomain,
                "success": False,
                "reason": f"Failed to add incoming mail MX record for {email_subdomain.subdomain}"
            }

        # Mark all emails under this subdomain as active.
        for email in email_subdomain.emailid_set.all():
            email.email_active = True
            email.save()

    # Check domain age for determining usability date.
    whois_result = whois.whois(managed_subdomain.subdomain)

    if (whois_result is not None) and ('creation_date' in whois_result) and (whois_result['creation_date'] is not None):
        creation_date: datetime.datetime = whois_result['creation_date']
        if (datetime.datetime.now() - creation_date).days >= 14:
            domain_usable_from = timezone.now() + relativedelta(days=14)
        else:
            domain_usable_from = timezone.now() + relativedelta(days=14)
    else:
        domain_usable_from = timezone.now() + relativedelta(days=14)

    managed_subdomain.current_setup_stage = next_stage_number
    managed_subdomain.setup_complete = True
    managed_subdomain.domain_usable_from = domain_usable_from
    managed_subdomain.save()

    # Send email to user.
    send_email_task.delay(
        to=user.email,
        sender="<EMAIL>",
        sender_name="Team DeliverymanAI",
        subject=f"Your domain {managed_subdomain.subdomain} is connected",
        body_html=domain_connected_email_message(
            username=user.username,
            domain_name=managed_subdomain.subdomain,
            warmup_completion_date=domain_usable_from.strftime("%d %b, %Y %H:%M UTC"),
            dashboard_url=os.environ["CE_APP_HOST_URL"] + "/dashboard",
            current_year=str(datetime.datetime.now().year),
        )
    )
    
    if not user.has_sent_domain_connected_email:
        user.has_sent_domain_connected_email = True
        user.has_sent_domain_connect_reminder_email = True
        user.save()

    check_blacklist_batch_task.delay([managed_subdomain.id])

    # Create first set of warmup schedules.
    create_warmup_schedules(managed_subdomain)

    return {
        "subdomain": subdomain,
        "success": True
    }


@shared_task
def camapign_reply_handler_task(sns_request_data: str):
    """
    Celery task that handles cmapaign email replies. If this is a first time reply, marks the schedule as replied,
    cancels all remaining scheduels for that contact and classifies the reply message.

    :param sns_request_data: request data (string) from SNS under "Message" key.
    """
    sns_message: Dict = json.loads(sns_request_data)
    raw_email_message_base64: str = sns_message["content"]
    raw_email_message: str = base64.b64decode(raw_email_message_base64).decode("utf-8")
    email_s3_key: str = sns_message["mail"]["messageId"]

    # Fetch the reply to message id.
    headers: List[Dict] = sns_message["mail"]["headers"]
    try:
        in_reply_to_dict: Dict = list(filter(lambda h: h["name"] == "In-Reply-To", headers))[0]
    except IndexError:
        logger.error(f"camapign_reply_handler_task() - "
                     f"In-Reply-To header is missing in email data. Ignoring event.")
        return {
            "success": False,
            "message": "In-Reply-To header is missing in email data. Ignoring event."
        }
    in_reply_to_message_id: str = extract_message_id(in_reply_to_dict["value"])

    # Fetch the campaign email schedule related to this message id.
    try:
        schedule: CampaignSchedule = CampaignSchedule.objects.get(message_id=in_reply_to_message_id)
    except CampaignSchedule.DoesNotExist:
        logger.error(f"camapign_reply_handler_task() - Could not find CampaignSchedule with "
                     f"message_id '{in_reply_to_message_id}'. Ignoring event.")
        return {
            "success": False,
            "message": f"Could not find CampaignSchedule with message_id '{in_reply_to_message_id}'. Ignoring event."
        }

    # Get the campaign uid for CampaignManager and logging.
    campaign_uid: str = schedule.campaign.uid

    # If reply was already received for this schedule, ignore the event.
    if schedule.reply_received:
        logger.info(f"Campaign {campaign_uid}: CampaignSchedule '{in_reply_to_message_id}' was already replied to. "
                    f"Ignoring event.")
        return {
            "success": True,
            "message": f"CampaignSchedule '{in_reply_to_message_id}' was already replied to. Ignoring event."
        }

    # Find the reply class.
    logger.debug(f"Campaign {campaign_uid}: Classifying reply...")
    try:
        reply_class: Literal["positive", "neutral", "negative", "bounced"] = classify_email_reply(raw_email_message)
    except Exception as err:
        logger.error(err)
        reply_class = "neutral"

    # If it's not a bounce, mark this schedule email as replied. Otherwise mark this email as bad/bounced.
    if reply_class != "bounced":
        logger.info(f"Campaign {campaign_uid}: Marking contact and schedules as replied...")
        cm = CampaignManager(campaign_uid)
        # noinspection PyTypeChecker
        cm.end_campaign_for_contact__reply_received(schedule, reply_class, email_s3_key)

        try:
            workspace: Workspace = Workspace.objects.get(campaign__uid=campaign_uid)
            workspace.total_replies_received += 1
            workspace.save()
        except Workspace.DoesNotExist:
            logger.error(
                f"Failed to increment reply counter. Workspace for campaign '{campaign_uid}' could not be found.")

        logger.info(f"Campaign {campaign_uid}: Reply for schedule '{schedule.uid}' has been processed successfully!")

        return {
            "success": True,
            "message": f"Reply for schedule '{schedule.uid}' has been processed successfully!"
        }

    else:
        # Add this to bad email list.
        try:
            BadEmail.objects.create(
                email_id=schedule.contact.email_id,
                reason="Bounced",
            )
        except IntegrityError:
            logger.info(f"Email id {schedule.contact.email_id} already exists in BadEmail table.")

        # Find all "created" and "sent" schedules across all campaigns and cancel them.
        schedules = CampaignSchedule.objects.filter(status__in=["created", "sent"], contact=schedule.contact)
        for schedule in schedules:
            # Delete schedule if it has not been sent already.
            if schedule.status == "created":
                aws_eventbridge.delete_schedule(
                    Name=schedule.schedule_name,
                )

            # Update the contact.
            schedule.contact.active = False
            schedule.contact.save()

            # Mark contact list contact as bounced.
            if schedule.contact.imported_contact_list_contact:
                schedule.contact.imported_contact_list_contact.bounced = True
                schedule.contact.imported_contact_list_contact.save()

            # Update status.
            schedule.status = "cancelled_bad_email"
            schedule.save()

            logger.debug(f"Schedule {schedule.schedule_name} has been deleted. Reason: bad email.")

        logger.debug(f"Creating bounce activity log for {schedule.contact.email_id}...")
        CampaignActivity.objects.create(
            campaign=schedules[0].campaign,
            event_subject="Bounce",
            event_from=schedule.contact.email_id,
        )

        return {
            "success": True,
            "message": f"Bounced reply for schedule '{schedule.uid}' has been processed successfully!"
        }


@shared_task
def contact_list_setup_task(contact_list_id: int,
                            contacts_data: List[List[str]],
                            csv_headers: List[str],
                            email_column_index: int):
    """
    Celery task to add contacts to a newly created contact list.
    """
    # Fetch the contact list.
    contact_list = ImportedContactList.objects.get(id=contact_list_id)
    try:
        # Remove any duplicate email from csv data.
        logger.info("contact_list_setup_task() - Removing duplicate rows for imported csv data...")
        contacts_data = remove_duplicate_rows(contacts_data, email_column_index)

        # Convert CSV data into List of dictionaries.
        converted_contacts_data: List[Dict] = csv_rows_to_dict(contacts_data, csv_headers)

        # Sort out the data.
        # We need to get email id from each contact and then put rest of the data as attributes.
        email_key: str = csv_headers[email_column_index]

        # Bulk create all contacts.
        bulk_create__contact = []
        for contact in converted_contacts_data:
            # We'll store all "attribute" data (i.e. contact data without email column) in attributes_json.
            attributes_json: Dict = {}

            try:
                email_id: str = html.escape(contact[email_key].strip().lower())
            except KeyError:
                # If email value is missing, ignore this one.
                logger.warning(f"contact_list_setup_task() - Missing email in contact data. Skipping...")
                continue

            # skip if blank.
            if not email_id:
                logger.warning(f"contact_list_setup_task() - Blank email in contact data. Skipping...")
                continue

            # Add all except for email column as attributes.
            for attribute in contact.keys():
                if attribute != email_key:
                    attributes_json.update({attribute: contact[attribute]})

            contact_uid = f"contact_{uuid.uuid4().hex}"
            bulk_create__contact.append(Contact(
                uid=contact_uid,
                contact_list=contact_list,
                email_id=email_id,
                attributes=attributes_json
            ))

        logger.info(f"contact_list_setup_task() - Bulk creating contacts for list {contact_list.uid}...")
        start = time.time()
        Contact.objects.bulk_create(bulk_create__contact, batch_size=5000)
        logger.info(f"contact_list_setup_task() - Bulk Contact creation took {time.time() - start:.2f} seconds.")

        # Mark the contact list as active.
        contact_list.status = "active"
        contact_list.save()

        # Send websocket message.
        time.sleep(5)
        contact_list.refresh_from_db()
        send_websocket_event(
            f"contact_list_page_{contact_list.workspace.user.id}",
            "contact_list_status_update",
            "contact_list_status_update",
            {
                "contact_list_uid": contact_list.uid,
                "new_status": "active",
                "total_contacts": contact_list.contact_set.count(),
                "total_columns": len(contact_list.contact_set.all()[0].get_attribute_names()) + 1  # +1 for email col.
            }
        )

        return {
            "success": True,
            "message": f"Contact list '{contact_list.uid}' has been created successfully!"
        }

    except Exception as err:
        logger.exception(err)

        # Mark the contact list as failed.
        contact_list.status = "failed"
        contact_list.save()

        # Send websocket message.
        time.sleep(5)
        send_websocket_event(
            f"contact_list_page_{contact_list.workspace.user.id}",
            "contact_list_status_update",
            "contact_list_status_update",
            {
                "contact_list_uid": contact_list.uid,
                "new_status": "failed",
                "total_contacts": 0,
                "total_columns": 0,
            }
        )

        return {
            "success": False,
            "message": f"Contact list '{contact_list.uid}' creation failed due to error!"
        }


@shared_task
def create_campaign_task(
        user_id: int,
        campaign_id: int,
        selected_contact_list_uid: str,
        selected_domain_ids: List[int],
):
    user = User.objects.get(id=user_id)
    campaign = Campaign.objects.get(id=campaign_id)

    # Fetch the contact list
    contact_list: ImportedContactList = ImportedContactList.objects.get(uid=selected_contact_list_uid)

    # Link contact lists to this campaign.
    campaign.selected_contact_lists.add(contact_list)

    # Add the selected managed subdomains.
    for msub_id in selected_domain_ids:
        try:
            msub = user.active_workspace.managedsubdomain_set.get(id=msub_id)
        except ManagedSubdomain.DoesNotExist:
            logger.error(f"create_campaign_task() - No ManagedSubdomain with id {msub_id} found in "
                         f"workspace {user.active_workspace.name}")
            campaign.delete()
            return {
                "success": False,
                "message": f"No ManagedSubdomain with id {msub_id} found in workspace {user.active_workspace.name}"
            }

        campaign.sending_domains.add(msub)
        logger.debug(f"Added managed subdomain '{msub.subdomain}' to campaign '{campaign.uid}'")

    # Fetch all unsubscribed and bad emails.
    unsubscribed_emails: List[str] = list(
        UnsubscribedEmail.objects.filter(
            workspace=user.active_workspace
        ).values_list("email_id", flat=True)
    )
    bad_emails: List[str] = list(
        BadEmail.objects.all().values_list("email_id", flat=True)
    )

    try:
        logger.info(f"Creating contacts for {campaign.uid}")
        bulk_create__campaign_ignored_contact = []
        bulk_create__campaign_contact = []
        for contact in contact_list.contact_set.all():
            # Create the contacts object if it's not present in unsubscribe or bad emails list.
            if contact.email_id in unsubscribed_emails:
                bulk_create__campaign_ignored_contact.append(CampaignIgnoredContacts(
                    campaign=campaign,
                    email_id=contact.email_id,
                    reason="Unsubscribed from your campaigns."
                ))

            elif contact.email_id in bad_emails:
                bulk_create__campaign_ignored_contact.append(CampaignIgnoredContacts(
                    campaign=campaign,
                    email_id=contact.email_id,
                    reason="Bad email address / other delivery issues."
                ))

            else:
                contact_uid = f"contact_{uuid.uuid4().hex}"
                bulk_create__campaign_contact.append(CampaignContact(
                    uid=contact_uid,
                    campaign=campaign,
                    email_id=contact.email_id,
                    attributes=contact.attributes,
                    imported_contact_list_contact=contact,
                ))

        CampaignIgnoredContacts.objects.bulk_create(bulk_create__campaign_ignored_contact, batch_size=5000)
        CampaignContact.objects.bulk_create(bulk_create__campaign_contact, batch_size=5000)

    except Exception as err:
        # In case of any error delete the created campaign.
        logger.exception(err)
        campaign.delete()
        return {
            "success": False,
            "message": f"{err}"
        }

    campaign.status = "created"
    campaign.save()

    # Wait for 5 seconds so user's page can load.
    time.sleep(5)

    # Send websocket message.
    send_websocket_event(
        f"campaigns_page_{user_id}",
        "campaign_status_update",
        "campaign_status_update",
        {
            "campaign_uid": campaign.uid,
            "new_status": "created",
        }
    )

    return {
        "success": True,
        "message": f"Create campaign task finished for campaign {campaign.uid}"
    }


@shared_task
def duplicate_campaign_task(target_campaign_uid: str, new_campaign_uid: str):
    """
    Celery task for campaign duplication feature. Copies over all values from target campaign to new camapign.

    :param target_campaign_uid: Values will be copied from here.
    :param new_campaign_uid: Copied values will be added here.
    """
    target_campaign: Campaign = Campaign.objects.get(uid=target_campaign_uid)
    new_campaign: Campaign = Campaign.objects.get(uid=new_campaign_uid)

    # Copy over all the ignored contacts.
    logger.debug("Copying over all ignored contacts...")
    for ignored_contact in target_campaign.campaignignoredcontacts_set.all():
        CampaignIgnoredContacts.objects.create(
            campaign=new_campaign,
            email_id=ignored_contact.email_id,
            reason=ignored_contact.reason,
        )

    # Copy over all the valid contacts.
    logger.debug("Copying over all valid contacts...")
    for contact in target_campaign.campaigncontact_set.all():
        contact_uid = f"contact_{uuid.uuid4().hex}"
        CampaignContact.objects.create(
            uid=contact_uid,
            campaign=new_campaign,
            email_id=contact.email_id,
            attributes=contact.attributes,
            imported_contact_list_contact=contact.imported_contact_list_contact,
        )

    # Copy over all the email messages.
    logger.debug("Copying over all email messages...")
    for email in target_campaign.campaignemailmessage_set.all():
        campaign_message_uid: str = f"campaign_message_{uuid.uuid4().hex}"
        CampaignEmailMessage.objects.create(
            campaign=new_campaign,
            uid=campaign_message_uid,
            order=email.order,
            subject=email.subject,
            body=email.body,
            email_content_type=email.email_content_type,
            next_message_days=email.next_message_days,
            score=email.score,
            verdict=email.verdict,
            suggestions=email.suggestions,
            spintax_version="v2",
        )

    new_campaign.status = "created"
    new_campaign.reply_to_address = target_campaign.reply_to_address
    new_campaign.save()

    # Copy over all the sending domains.
    logger.debug("Copying over all sending domains...")
    for msub in target_campaign.sending_domains.all():
        new_campaign.sending_domains.add(msub)

    # Wait for 5 seconds so user's page can load.
    time.sleep(5)

    # Send websocket message.
    send_websocket_event(
        f"campaigns_page_{new_campaign.workspace.user.id}",
        "campaign_status_update",
        "campaign_status_update",
        {
            "campaign_uid": new_campaign_uid,
            "new_status": "created",
        }
    )

    return {
        "success": True,
        "message": f"Campaign '{target_campaign_uid}' values copied over to '{new_campaign_uid}' successfully."
    }


@shared_task
def delete_workspace_task(workspace_id: int):
    """
    Celery task to delete given workspace. Does not move user to another workspace so if needed,
    pelase take care of that before calling this function.

    :param workspace_id: workspace model id value.
    """
    try:
        workspace: Workspace = Workspace.objects.get(id=workspace_id)
    except Workspace.DoesNotExist:
        logger.critical(f"delete_workspace_task: No Workspace with id {workspace_id} was found.")
        return {
            "success": False,
            "message": f"No Workspace with id {workspace_id} was found."
        }

    # Delete all campaigns.
    logger.debug(f"Deleting all campaigns for workspace {workspace_id}...")
    workspace.campaign_set.all().delete()

    # Delete all managed subdomains.
    for msub in workspace.managedsubdomain_set.all():
        # Remove managed subdomain from SES Identity.
        try:
            aws_ses.delete_email_identity(EmailIdentity=msub.subdomain)
        except aws_ses.exceptions.NotFoundException:
            logger.warning(f"remove_managed_subdomain() - SES Identity {msub.subdomain} not found. Skipping.")

        # Remove email subdomain from SES Identity.
        for esub in msub.emailsubdomain_set.all():
            try:
                aws_ses.delete_email_identity(EmailIdentity=esub.subdomain)
            except aws_ses.exceptions.NotFoundException:
                logger.warning(f"remove_managed_subdomain() - SES Identity {msub.subdomain} not found. Skipping.")

        # Delete Route53 hosted zone. We need to remove the records first, then delete the zone.
        if msub.hosted_zone_id:
            max_deletion_runs = 100
            while max_deletion_runs > 0:
                max_deletion_runs -= 1
                # Get all records in zone except for default NS and SOA.
                result = aws_route53.list_resource_record_sets(HostedZoneId=msub.hosted_zone_id, MaxItems="300")

                records: List[Dict] = result["ResourceRecordSets"]
                filtered_records: List[Dict] = list(filter(lambda x: x["Type"] not in ["NS", "SOA"], records))

                if filtered_records:
                    # Delete the records.
                    delete_route53_records(msub.hosted_zone_id, filtered_records)

                    # If there are no more records, break.
                    if not result["IsTruncated"]:
                        logger.debug("list_resource_record_sets result is not truncated. Breaking...")
                        break

                    # Wait for 6 seconds before fetching the next batch, so we don't hit rate limit.
                    time.sleep(6)

                else:
                    # Break if there are no more records (just an alternative if 'IsTruncated' check doesn't work).
                    logger.debug("list_resource_record_sets result is empty. Breaking...")
                    break

            # Delete the zone.
            aws_route53.delete_hosted_zone(
                Id=msub.hosted_zone_id
            )

        else:
            logger.warning(f"remove_managed_subdomain() - Failed to delete Route53 hosted zone for "
                           f"managed subdomain {msub.subdomain}. Missing 'hosted_zone_id' value. Skipping.")

        # Delete database entries.
        msub.delete()

    # Delete the workspace.
    workspace.delete()

    return {
        "success": True,
        "message": f"Deleted workspace {workspace_id} successfully."
    }


@shared_task
def cancel_all_campaigns_task(user_id: int):
    """
    Celery task to cancel all creating/created/scheduled/running campaigns for given user.

    :param user_id: User database id.
    """
    user = User.objects.get(id=user_id)

    logger.info(f"Cancelling all campaigns for user {user.email}...")

    for camp in Campaign.objects.filter(
            workspace__user=user,
            status__in=["creating", "created", "scheduled", "running"]
    ):
        cm = CampaignManager(camp)
        cm.stop_campaign()
        logger.debug(f"Stopped campaign {camp.uid}")

    logger.info(f"Campaigns blocked and all running/scheduled campaigns cancelled for account {user.email}")

    return {
        "success": True,
        "message": f"Cancelled all campaigns for user {user.email}"
    }


@shared_task
def send_resume_campaign_email_task(workspace_id: str):
    """
    Task to send resume campaign emails to user when their plans are renewed and credits are added.

    :param workspace_id: Workspace model object id.
    """
    sixty_days_ago = timezone.now() - timedelta(days=60)
    workspace = Workspace.objects.get(id=workspace_id)
    user = workspace.user

    paused_campaigns = workspace.campaign_set.filter(status="paused", campaign_paused_on__gte=sixty_days_ago)
    if paused_campaigns.count() > 0:
        # Send the forgot password email.
        message: str = plan_renewal_paused_campaigns_email_message(
            user.username,
            list(paused_campaigns.values_list("name", flat=True)),
            workspace.name
        )
        send_email(
            to=user.email,
            sender="<EMAIL>",
            sender_name="Deliveryman.ai",
            subject="Your campaigns are waiting to be resumed",
            body_html=message,
        )

        logger.info(f"Resume paused campaigns email task to user {user.email} for workspace id {workspace.id}")

    else:
        logger.info(f"No paused campaigns for user {user.email} in workspace id {workspace.id}")

    return {
        "success": True,
        "message": f"send_resume_campaign_email_task completed for {user.email} (workspace id: {workspace_id})"
    }


@shared_task
def start_campaign_task(campaign_uid: str):
    """
    Task to create all schedules and start campaign.

    :param campaign_uid: Campaign model object UID.
    """
    cm = CampaignManager(campaign_uid)
    cm.start_campaign()

    # Send email to user.
    camp = Campaign.objects.get(uid=campaign_uid)
    user = camp.workspace.user
    if not user.has_sent_campaign_started_email:
        send_email_task.delay(
            to=user.email,
            sender="<EMAIL>",
            sender_name="Team DeliverymanAI",
            subject=f"Your campaign \"{camp.name}\" is live",
            body_html=campaign_start_email_message(
                username=user.username,
                leads_count=camp.campaigncontact_set.count(),
                sequence_count=camp.campaignemailmessage_set.count(),
                completion_date=camp.campaignschedule_set.all().order_by("-schedule_datetime")[
                    0].schedule_datetime.strftime("%d %b, %Y"),
                campaign_details_url=os.environ["CE_APP_HOST_URL"] + f"/campaigns/{campaign_uid}",
                current_year=str(datetime.datetime.now().year),
            )
        )
        user.has_sent_campaign_started_email = True
        user.save()
        logger.info(f"Campaign start email sent to {user.email}")

    return {
        "success": True,
        "message": f"Campaign {campaign_uid} started successfully."
    }


@shared_task
def cancel_campaign_task(campaign_uid: str):
    """
    Task to delete all aws schedules and fully cancel the campaign.

    :param campaign_uid: Campaign model object UID.
    """
    cm = CampaignManager(campaign_uid)
    cm.stop_campaign()

    return {
        "success": True,
        "message": f"Campaign {campaign_uid} has been cancelled."
    }


@shared_task
def pause_campaign_task(campaign_uid: str):
    """
    Task to delete all aws schedules and pause the campaign.

    :param campaign_uid: Campaign model object UID.
    """
    cm = CampaignManager(campaign_uid)
    cm.pause_campaign()

    return {
        "success": True,
        "message": f"Campaign {campaign_uid} has been paused."
    }


@shared_task
def resume_campaign_task(campaign_uid: str):
    """
    Resumes given campaign.

    :param campaign_uid: Campaign DB uid.
    """
    camp = Campaign.objects.get(uid=campaign_uid)

    cm = CampaignManager(camp)
    cm.resume_campaign()

    return {
        "success": True,
        "message": f"Campaign {campaign_uid} has been resumed."
    }


@shared_task
def send_email_task(to: str | List,
                    sender: str,
                    sender_name: str,
                    subject: str,
                    body_html: str | None = None,
                    body_plaintext: str | None = None,
                    files: Optional[List[InMemoryUploadedFile]] = None,
                    reply_to: Optional[str] = None):
    """
    Same as send_email in utils.py but runs in backround as a celery task.

    Args:
        to (str | List): The recipient's email address or a list of email addresses.
        sender (str): The sender's email address.
        sender_name (str): The name of the sender to appear in the email.
        subject (str): The subject line of the email.
        body_html (str | None, optional): The HTML content of the email body. Default is None.
        body_plaintext (str | None, optional): The plaintext content of the email body. Default is None.
        files (Optional[List[InMemoryUploadedFile]], optional): A list of file attachments.
            Default is None.
        reply_to (Optional[str], optional): An optional reply-to email address. Default is None.
    """
    send_email(to, sender, sender_name, subject, body_html, body_plaintext, files, reply_to)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def check_blacklist_batch_task(self, subdomain_ids: list):
    try:
        checker = CheckDNSBLDomain()
        subdomains = ManagedSubdomain.objects.filter(id__in=subdomain_ids).select_related('workspace__user')

        for sub in subdomains:
            try:
                if BlacklistCheckStatus.objects.filter(subdomain=sub, checked_on=date.today()).exists():
                    continue

                prev_entry = BlacklistCheckStatus.objects.filter(subdomain=sub).order_by('-checked_on').first()
                prev_sources = prev_entry.blacklisted_sources if prev_entry else []

                result = checker.check_domain_in_dnsbl_threaded(sub.subdomain)
                logger.info(f"domain : {sub.subdomain} result : {result}")
                blacklisted_sources = result.get("listed_in", [])

                if blacklisted_sources is None:
                    blacklisted_sources = ["Unknown error"]
                elif not isinstance(blacklisted_sources, list):
                    blacklisted_sources = [str(blacklisted_sources)]

                error_keywords = ["Unknown error"]
                was_previously_blacklisted = (
                        prev_sources and any(src not in error_keywords for src in prev_sources)
                )
                is_now_clean = len(blacklisted_sources) == 0

                # Save the blacklist check result
                BlacklistCheckStatus.objects.create(
                    subdomain=sub,
                    blacklisted_sources=blacklisted_sources
                )

                user = sub.workspace.user if sub.workspace and sub.workspace.user else None
                blacklist_email = sub.workspace.blacklist_email if sub.workspace else False
                user_email = user.email if user else None
                if not user_email:
                    continue

                alert_status, _ = BlacklistAlertStatus.objects.get_or_create(subdomain=sub)

                if was_previously_blacklisted and is_now_clean and blacklist_email:
                    alert_status.email_count = 0
                    alert_status.last_sent = None
                    alert_status.save()

                    send_email(
                        to=user_email,
                        sender="<EMAIL>",
                        sender_name="Team DeliverymanAI",
                        subject=f"Your domain {sub.subdomain} is no longer blacklisted",
                        body_html=domain_blacklisted_removed_email_message(
                            username=user.username,
                            domain_name=sub.subdomain,
                            campaign_url=os.environ["CE_APP_HOST_URL"] + "/campaigns",
                            current_year=str(datetime.datetime.now().year),
                        ),
                    )

                # CASE 2: Domain is blacklisted and valid for email
                elif (blacklisted_sources and blacklisted_sources not in [["Unknown error"]]) and blacklist_email:
                    can_send = False
                    today = date.today()

                    if alert_status.email_count < 4:
                        if not alert_status.last_sent or (today - alert_status.last_sent) >= timedelta(days=8):
                            can_send = True

                    if can_send:
                        sources_html = "".join(f"<li>{escape(s)}</li>" for s in blacklisted_sources)
                        send_email(
                            to=user_email,
                            sender="<EMAIL>",
                            sender_name="Team DeliverymanAI",
                            subject=f"Your domain {sub.subdomain} is found in a blacklist",
                            body_html=domain_blacklisted_email_message(
                                username=user.username,
                                domain_name=sub.subdomain,
                                blacklisted=sources_html,
                                current_year=str(datetime.datetime.now().year),
                            ),
                        )
                        alert_status.email_count += 1
                        alert_status.last_sent = today
                        alert_status.save()

            except Exception as e:
                logger.critical(f"Error processing {sub.subdomain}: {e}")
                continue

    except Exception as e:
        logger.critical(f"Blacklist batch task failed: {e}")
        raise self.retry(exc=e)


@shared_task
def delete_managed_subdomain_task(domain: str):
    """
    Celery task for deleting a connected domain.
    """
    msub = ManagedSubdomain.objects.get(subdomain=domain)

    # Remove managed subdomain from SES Identity.
    try:
        aws_ses.delete_email_identity(EmailIdentity=domain)
    except aws_ses.exceptions.NotFoundException:
        logger.warning(f"remove_managed_subdomain() - SES Identity {domain} not found. Skipping.")

    # Remove email subdomain from SES Identity.
    for esub in msub.emailsubdomain_set.all():
        try:
            aws_ses.delete_email_identity(EmailIdentity=esub.subdomain)
        except aws_ses.exceptions.NotFoundException:
            logger.warning(f"remove_managed_subdomain() - SES Identity {msub.subdomain} not found. Skipping.")

    # Delete Route53 hosted zone. We need to remove the records first, then delete the zone.
    if msub.hosted_zone_id:
        while True:
            # Get all records in zone except for default NS and SOA.
            result = aws_route53.list_resource_record_sets(
                HostedZoneId=msub.hosted_zone_id
            )

            records: List[Dict] = result["ResourceRecordSets"]
            filtered_records: List[Dict] = list(filter(lambda x: x["Type"] not in ["NS", "SOA"], records))

            if filtered_records:
                # Delete the records.
                delete_route53_records(msub.hosted_zone_id, filtered_records)

                # If there are no more records, break.
                if not result["IsTruncated"]:
                    break

                # Wait for 6 seconds before fetching the next batch, so we don't hit rate limit.
                time.sleep(6)

            else:
                # Break if there are no more records (just an alternative if 'IsTruncated' check doesn't work).
                break

        # Delete the zone.
        aws_route53.delete_hosted_zone(
            Id=msub.hosted_zone_id
        )

        # Remove google postmaster integration for this domain/subdomain.
        user: User = msub.workspace.user
        try:
            if user.google_postmaster_integration:
                current_domain_list: List[GooglePostmasterDomainSchema] = [
                    GooglePostmasterDomainSchema(**item) for item in user.google_postmaster_integration.domains_added
                ]

                updated_domain_list: List[GooglePostmasterDomainSchema] = list(
                    filter(lambda x: x.domain != domain, current_domain_list)
                )

                user.google_postmaster_integration.domains_added = [item.model_dump() for item in updated_domain_list]
                user.google_postmaster_integration.save()

        except Exception as err:
            logger.critical(err, exc_info=True)

    else:
        logger.warning(f"remove_managed_subdomain() - Failed to delete Route53 hosted zone for "
                       f"managed subdomain {msub.subdomain}. Missing 'hosted_zone_id' value. Skipping.")

    # Delete database entries.
    msub.delete()

    logger.info(f"Connected domain {domain} has been deleted successfully!")
