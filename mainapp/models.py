from typing import List

from django.contrib.auth.base_user import BaseUserManager, AbstractBaseUser
from django.contrib.auth.hashers import make_password
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone

from pydantic import BaseModel, field_validator, RootModel
from zoneinfo import ZoneInfo


# Pydantic model for validation
class SendingLimitRule(BaseModel):
    days: int
    emails: int

    @field_validator('days', mode='after')
    def validate_days(cls, v):
        if v <= 0:
            raise ValueError("'days' must be positive non-zero value.")
        return v

    @field_validator('emails', mode='after')
    def validate_emails(cls, v):
        if v <= 0:
            raise ValueError("'emails' must be positive non-zero value.")
        return v


class SendingLimitRuleList(RootModel):
    root: list[SendingLimitRule]

    def __iter__(self):
        return iter(self.root)

    def __getitem__(self, item):
        return self.root[item]


def get_default_sending_limits():
    return [
        {
            "days": 2,
            "emails": 200,
        },
        {
            "days": 8,
            "emails": 400,
        },
        {
            "days": 15,
            "emails": 800,
        },
        {
            "days": 22,
            "emails": 1200,
        }
    ]


class SiteConfiguration(models.Model):
    # These global limits will be applied from date of ManagedSubdomain creation.
    domain_sending_limits = models.JSONField(default=get_default_sending_limits)


class CustomUserManager(BaseUserManager):
    def create_user(self, username: str,
                    email: str,
                    password: str,
                    **extra_fields):
        email = self.normalize_email(email)
        # self.model calls User class with provided arguments
        user = self.model(username=username, email=email, **extra_fields)
        user.password = make_password(password)
        user.save()

        return user


class FreePlanIntegrityError(Exception):
    def __init__(self):
        super().__init__(f"Only one default free plan is allowed.")


class SubscriptionPlan(models.Model):
    """
    A free/paid plan that user can subscribe to.
    """
    class Meta:
        ordering = ["display_order"]

    is_free_plan = models.BooleanField(default=False)

    plan_name = models.CharField(max_length=500, blank=False, null=False)
    display_order = models.PositiveIntegerField(default=0)
    plan_tier = models.PositiveIntegerField(default=0)
    hidden = models.BooleanField(default=False)  # For showing/hiding subscription plan card.
    product_id = models.CharField(max_length=100, blank=False, null=True)  # NULL for free / non-stripe plan.
    created_on = models.DateTimeField(default=timezone.now)

    monthly_price_id = models.CharField(max_length=100, blank=False, null=True)  # NULL for free / non-stripe plan.
    annual_price_id = models.CharField(max_length=100, blank=False, null=True)  # NULL for free / non-stripe plan.

    monthly_amount = models.PositiveIntegerField()
    annual_amount = models.PositiveIntegerField()

    monthly_feature_list = models.JSONField(default=list)
    annual_feature_list = models.JSONField(default=list)

    popular = models.BooleanField(default=False)

    # Limits:
    monthly_email_sending_quota = models.PositiveIntegerField()
    annual_email_sending_quota = models.PositiveIntegerField(default=0)
    total_domain_connections_allowed = models.PositiveIntegerField(default=0)

    def save(self, **kwargs):
        # If the save value has "is_free_plan" set to True, we need to check if there are any existing plans with
        # is_free_plan as True.
        if (self.pk is None) and self.is_free_plan:
            if SubscriptionPlan.objects.filter(is_free_plan=True).count() > 0:
                raise FreePlanIntegrityError()

        super().save(**kwargs)

    def __str__(self):
        return self.plan_name


class User(AbstractBaseUser):
    """
    Custom user model. Inherits AbstractBaseUser model. 'password' and 'last_login' are inherited from base class
    """
    objects = CustomUserManager()

    # Staff roles
    is_admin = models.BooleanField(default=False)

    # Main user fields
    email = models.EmailField(max_length=254, unique=True, db_index=True)
    username = models.CharField(max_length=150, blank=False, null=False)
    country = models.CharField(max_length=500, blank=False, null=False, default="United States")
    signup_plan_selection_done = models.BooleanField(default=False)
    has_purchased_plan = models.BooleanField(default=False)  # If user has purchased a plan at least once.

    # Integration
    google_api_auth_creds = models.BinaryField(blank=False, null=True, default=None)
    google_auth_request_uid = models.TextField(blank=False, null=True, default=None)
    google_postmaster_integration = models.ForeignKey("GooglePostmasterIntegration",
                                                      on_delete=models.SET_NULL, null=True, default=None)

    # Reset password
    reset_password_token = models.TextField(blank=False, null=True, default=None)
    reset_password_request_date = models.DateTimeField(blank=False, null=True, default=None)

    # Workspace
    active_workspace = models.OneToOneField("Workspace", on_delete=models.PROTECT, null=True, default=None,
                                            related_name="active_workspace_user")

    # Stripe
    stripe_customer_id = models.CharField(max_length=500, null=True, blank=False, default=None)
    is_new_user_discount_eligible = models.BooleanField(default=True)
    is_trial_period_eligible = models.BooleanField(default=True)

    first_domain_connected_on = models.DateTimeField(null=True, default=None)

    # Emails Sent.
    has_sent_domain_connected_email = models.BooleanField(default=False)
    has_sent_domain_connect_reminder_email = models.BooleanField(default=False)  # This should be set to true on domain connection.
    has_sent_domain_warmed_up_email = models.BooleanField(default=False)
    has_sent_campaign_creation_reminder_email = models.BooleanField(default=False)  # Should be set to true on camapign creation.
    has_sent_campaign_started_email = models.BooleanField(default=False)
    has_sent_campaign_completed_email = models.BooleanField(default=False)

    # Misc.
    is_campaign_blocked = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    # email verify
    email_verified = models.BooleanField(default=False)

    # activity tracking
    @property
    def last_activity_time(self) -> str:
        """
        Returns the last activity time for the user.
        """
        user_activities: models.QuerySet[UserActivity] = self.useractivity_set.all()
        if user_activities.count() == 0:
            return None

        return user_activities.order_by("-last_activity_time").first().last_activity_time

    @property
    def last_activity_performed(self) -> str:
        """
        Returns the last activity performed by the user.
        """
        user_activities: models.QuerySet[UserActivity] = self.useractivity_set.all()
        if user_activities.count() == 0:
            return None

        user_last_activity: UserActivity = user_activities.order_by("-last_activity_time").first()
        ip_address: str = user_last_activity.ip_address or "N/A"
        api_endpoint: str = user_last_activity.api_endpoint

        # Convert datetime to IST.
        ist_zone = ZoneInfo("Asia/Kolkata")
        last_activity_time: str = user_last_activity.last_activity_time.astimezone(ist_zone).strftime("%d/%m/%Y %I:%M %p")
        return f"User performed last activity on {last_activity_time} from IP Address {ip_address} on API Endpoint {api_endpoint}"


    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']  # only for createsuperuser

    def __str__(self):
        return self.email


class UserActivity(models.Model):
    """
    Stores each event/activity for a user.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    last_activity_time = models.DateTimeField(default=timezone.now)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    api_endpoint = models.TextField()


class Workspace(models.Model):
    BILLING_PERIOD = [
        ("monthly", "Monthly"),
        ("annual", "Annual"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, default=None)
    name = models.CharField(max_length=300, blank=False, null=False, default="Default Workspace")
    created_on = models.DateTimeField(auto_now_add=True)

    # ---------- These are just for stats ----------
    total_emails_sent = models.PositiveIntegerField(default=0)
    total_replies_received = models.PositiveIntegerField(default=0)
    # ----------------------------------------------

    # Subscription plan based limits.
    credits_remaining = models.IntegerField(default=0)
    # limits_last_renewed_on = models.DateTimeField(default=timezone.now)
    next_renewal_date = models.DateTimeField(default=timezone.now)

    # Current active subscription plan.
    subscription_plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT, null=True, default=None)
    billing_period = models.CharField(max_length=100, blank=False, null=True, choices=BILLING_PERIOD, default=None)
    stripe_subscription_id = models.CharField(max_length=500, blank=False, null=True, default=None)

    # Blacklist Email
    blacklist_email = models.BooleanField(default=True)
    time_zone = models.CharField(max_length=100, default="UTC")

    def __str__(self):
        return self.name


# ================================================================
# ----------------------- DOMAINS & EMAILS -----------------------
# ================================================================


class ManagedSubdomain(models.Model):
    """
    Subdomain managed by us (Route53) for a user account.
    """
    STATUS = [
        ("warmup", "Warmup"),
        ("active", "Active"),
    ]

    NAMING_STRATEGIES = [
        ("random", "random"),
        ("male", "male"),
        ("female", "female"),
        ("custom", "custom"),
    ]

    # user = models.ForeignKey(User, on_delete=models.CASCADE)
    # null=True is only added for it to work in existing database. Please do not use null here.
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE, null=True, default=None)
    subdomain = models.CharField(max_length=500, unique=True)
    created_on = models.DateTimeField(default=timezone.now)

    # -------- Setup --------
    setup_complete = models.BooleanField(default=False)
    current_setup_stage = models.IntegerField(default=0)
    naming_strategy = models.CharField(max_length=300, null=False, blank=False, choices=NAMING_STRATEGIES,
                                       default="random")
    # Will only be considered if naming_strategy is "custom".
    custom_name = models.CharField(max_length=500, null=True, blank=False, default=None)
    contacts_count = models.PositiveIntegerField(default=1)  # This cannot be 0.
    dkim_tokens = models.JSONField(default=list)

    # -------- AWS Route53 --------
    hosted_zone_id = models.CharField(max_length=500, null=True, default=None)

    # -------- Celery Task --------
    celery_naming_strategy_task_id = models.CharField(max_length=250, null=True, default=None)
    celery_setup_emails_task_id = models.CharField(max_length=250, null=True, default=None)

    # -------- Domain Warmup --------
    warmup_email_count = models.FloatField(null=True, blank=False, default=15.0)
    domain_usable_from = models.DateTimeField(default=timezone.now)

    # -------- Domain Redirection --------
    # If None, it means domain redirection was removed or not set up.
    redirect_subdomains_to = models.CharField(null=True, blank=False, default=None)

    # -------- Email Sending Limits --------
    email_limit = models.PositiveIntegerField(default=200)
    email_limit_last_updated_on = models.DateTimeField(null=True, default=None)
    # None means it has not been used yet / has been a long time since last used.
    last_email_sent_on = models.DateTimeField(null=True, default=None)

    status = models.CharField(null=False, blank=False, choices=STATUS, default="active")

    def __str__(self):
        return self.subdomain


# noinspection PyUnusedLocal
@receiver(post_save, sender=ManagedSubdomain)
def check_contacts_count(sender, instance, **kwargs):
    if instance.contacts_count <= 0:
        raise ValidationError(message="contacts_count cannot be 0 in ManagedSubdomain")


class EmailSubdomain(models.Model):
    """
    Email subdomains we create for a managed subdomain.
    """

    class Meta:
        ordering = ['created_on']

    managed_subdomain = models.ForeignKey(ManagedSubdomain, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

    subdomain = models.CharField(max_length=500, unique=True)

    dkim_tokens = models.JSONField(default=list)

    def __str__(self):
        return self.subdomain


class EmailID(models.Model):
    """
    Email ID associated with an EmailSubdomain.
    """
    email_subdomain = models.ForeignKey(EmailSubdomain, on_delete=models.CASCADE)
    email_address = models.CharField(max_length=1000, unique=True, null=False, blank=False)
    username = models.CharField(max_length=500, null=False, blank=False, default="Hello")

    # When this is True, prevent certain actions like editing of email id.
    email_active = models.BooleanField(default=False)

    def get_active_contacts_count(self):
        """
        Returns the number of active contacts this email ID is associated with.
        """
        return self.campaigncontact_set.filter(active=True).count()

    def __str__(self):
        return self.email_address

# =========================================================
# --------------------- CONTACT LISTS ---------------------
# =========================================================


class ImportedContactList(models.Model):
    """
    Represents a list of imported contacts associated with a workspace.
    """
    STATUS = [
        ("uploading", "Uploading"),
        ("active", "Active"),
        ("failed", "Failed"),
    ]

    uid = models.CharField(max_length=255, unique=True)
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

    name = models.CharField(max_length=300, null=False, blank=False)

    status = models.CharField(max_length=100, null=False, blank=False, default="uploading")

    def __str__(self):
        return self.name


class Contact(models.Model):
    """
    Represents a single contact data in a given ImportedContactList.
    """
    uid = models.CharField(max_length=255, unique=True)
    contact_list = models.ForeignKey(ImportedContactList, on_delete=models.CASCADE)

    email_id = models.CharField(max_length=500, null=False, blank=False)
    attributes = models.JSONField(default=dict)

    bounced = models.BooleanField(default=False)
    unsubscribed = models.BooleanField(default=False)

    def get_attribute_names(self) -> List[str]:
        """
        Returns list of email variable names.
        """
        if self.attributes and (type(self.attributes) is dict):
            return list(self.attributes.keys())
        else:
            return []

    def __str__(self):
        return self.email_id


# =========================================================
# ----------------------- CAMPAIGNS -----------------------
# =========================================================

class Campaign(models.Model):
    """
    Model for email campaigns associated with each account.
    """
    STATUS = [
        ("creating", "Creating"),
        ("created", "Created"),
        ("scheduled", "Scheduled"),
        ("running", "Running"),
        ("paused", "Paused"),
        ("complete", "Complete"),
        ("cancelled", "Cancelled"),
    ]

    UNSUB_LINK_TYPE = [
        ("custom", "Custom"),
        ("auto", "Auto"),
    ]

    uid = models.CharField(max_length=255, unique=True)
    # user = models.ForeignKey(User, on_delete=models.CASCADE, db_index=True)
    # null=True is only added for it to work in existing database. Please do not use null here.
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE, null=True, default=None)
    created_on = models.DateTimeField(auto_now_add=True)

    name = models.CharField(max_length=300, null=False, blank=False)
    emails_per_day = models.PositiveIntegerField(default=0)  # If 0, we'll use our limits.
    skip_days = models.JSONField(default=list)
    test_email_destinations = models.JSONField(default=list)
    status = models.CharField(max_length=100, choices=STATUS, default="creating", null=False, blank=False)

    is_html_email = models.BooleanField(default=False)
    add_unsub_link = models.BooleanField(default=False)
    unsub_link_type = models.CharField(max_length=100, null=False, blank=False, default="auto")
    custom_unsub_text = models.TextField(null=False, blank=False, default="Want to opt out? Just reply with")

    # If set, we'll check for these in a cron job and start them when it is time.
    # Should be set back to NULL when campaign starts - either manually or because of schedule.
    scheduled_start_datetime = models.DateTimeField(null=True, default=None)

    reply_to_address = models.CharField(max_length=254, blank=False, null=True, default=None)
    sending_domains = models.ManyToManyField(ManagedSubdomain)
    selected_contact_lists = models.ManyToManyField(ImportedContactList)

    bounces = models.PositiveIntegerField(default=0)

    # Pause Campaign when credits reach 0.
    # When pausing campaign, `campaign_paused` will be set to True, `status` will be changed to "paused" and
    # `campaign_paused_on` will be set to current UTC date. When unpausing campaign, `campaign_paused` will be set
    # to False, `status` will be changed to "running" and `campaign_paused_on` will be set to None.
    campaign_paused = models.BooleanField(default=False)
    campaign_paused_on = models.DateTimeField(null=True, default=None)

    is_generating_schedules = models.BooleanField(default=False)
    is_deleting_schedules = models.BooleanField(default=False)

    archived = models.BooleanField(default=False)

    is_campaign_bounce_blocked = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class CampaignEmailMessage(models.Model):
    """
    Holds email messages for a campaign.
    """

    class Meta:
        ordering = ['order']

    LABELS = [
        ("passed", "Passed"),
        ("blocked", "Blocked"),
        ("rejected", "Rejected"),
        ("approved", "Approved"),
    ]

    EMAIL_CONTENT_TYPES = [
        ("text/plain", "Plain Text"),
        ("text/html", "HTML"),
    ]

    uid = models.CharField(max_length=255, unique=True)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE)
    order = models.IntegerField(default=0)

    subject = models.TextField(null=False, blank=False)
    body = models.TextField(null=False, blank=False)
    next_message_days = models.PositiveSmallIntegerField(null=False, blank=False, default=1)

    # Spam Rate Score
    score = models.PositiveIntegerField(null=True, default=None)  # 0 to 100 (percent)
    verdict = models.TextField(blank=True, null=True, default=None)
    suggestions = models.JSONField(default=list)  # List of string.

    label = models.CharField(max_length=300, choices=LABELS, null=False, blank=False, default="passed")
    blocked_reason = models.TextField(blank=False, null=True, default=None)
    is_flagged_for_approval = models.BooleanField(default=False)

    spintax_version = models.CharField(max_length=100, null=False, blank=False, default="v1")

    email_content_type = models.CharField(max_length=300, choices=EMAIL_CONTENT_TYPES, null=False, blank=False,
                                          default="text/plain")

    def __str__(self):
        return self.uid


class CampaignContact(models.Model):
    """
    Holds contact (target user) details for a campaign. Each contact should have an email id and (optionally)
    other attributes.
    """
    uid = models.CharField(max_length=255, unique=True, db_index=True)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, db_index=True)
    created_on = models.DateTimeField(auto_now_add=True)

    email_id = models.CharField(max_length=500, null=False, blank=False)
    attributes = models.JSONField(default=dict)

    sending_email = models.ForeignKey(EmailID, on_delete=models.SET_NULL, null=True, default=None)
    # NOTE: Not being used currently.
    email_schedule_names = models.JSONField(default=list)

    # Set this to True when no further emails need to be sent to this contact.
    # NOTE: Not being used currently.
    active = models.BooleanField(default=True)

    imported_contact_list_contact = models.ForeignKey(Contact, on_delete=models.SET_NULL, null=True, default=None)

    def get_attribute_names(self) -> List[str]:
        """
        Returns list of email variable names.
        """
        if self.attributes and (type(self.attributes) is dict):
            return list(self.attributes.keys())
        else:
            return []

    def __str__(self):
        return self.email_id


class CampaignIgnoredContacts(models.Model):
    """
    Holds contacts that were ignored due to being in unsubscribed lsit / bad email list.
    """
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, db_index=True)
    email_id = models.CharField(max_length=500, null=False, blank=False)
    reason = models.TextField(null=False, blank=False)

    def __str__(self):
        return self.email_id


class CampaignSchedule(models.Model):
    """
    Connects campaign contact with a campaign email message and holds scheduling and other data.
    """
    STATUS = [
        ("created", "Created"),
        ("scheduled", "Scheduled"),
        ("sent", "Sent"),
        ("failed", "Failed"),
        ("replied", "Replied"),
        ("cancelled_reply_received", "Cancelled - Reply Received"),
        ("cancelled_unsubscribed", "Cancelled - Unsubscribed"),
        ("cancelled_bad_email", "Cancelled - Bad Email"),
        ("cancelled_campaign_stopped", "Cancelled - Campaign Stopped"),
    ]

    REPLY_CLASSIFICATIONS = [
        ("positive", "Positive"),
        ("neutral", "Neutral"),
        ("negative", "Negative"),
    ]

    uid = models.CharField(max_length=255, unique=True, db_index=True)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, default=None)
    contact = models.ForeignKey(CampaignContact, on_delete=models.CASCADE)
    email_message = models.ForeignKey(CampaignEmailMessage, on_delete=models.CASCADE)

    schedule_name = models.CharField(max_length=500, null=True, blank=False)
    schedule_datetime = models.DateTimeField(null=True)

    status = models.CharField(max_length=100, choices=STATUS, default="created", null=False, blank=False)
    sent_on = models.DateTimeField(null=True, default=None)
    # AWS message id for tracking replies.
    message_id = models.CharField(max_length=100, null=True, blank=False, default=None, db_index=True)
    # S3 bucket key for stored email message.
    email_s3_key = models.CharField(max_length=100, null=True, blank=False, default=None, db_index=True)
    reply_received = models.BooleanField(default=False)
    reply_classification = models.CharField(max_length=100, choices=REPLY_CLASSIFICATIONS,
                                            null=True, blank=False, default=None, db_index=True)

    # Open tracking.
    email_opened = models.BooleanField(default=False)
    email_opened_on = models.DateTimeField(null=True, default=None)

    # # Calculated email limit value for this set of schedules. Used by "email sent" API to update ManagedSubdomain
    # # email limit value.
    # email_limit = models.PositiveIntegerField(default=0)

    def __str__(self):
        return self.schedule_datetime.strftime("%Y-%m-%dT%H:%M:%S")


class UnsubscribedEmail(models.Model):
    """
    Email IDs that we should not send emails to, for each account (user).
    """
    # user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    # null=True is only added for it to work in existing database. Please do not use null here.
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE, null=True)
    email_id = models.CharField(max_length=500, unique=True, db_index=True)
    added_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.email_id


class BadEmail(models.Model):
    """
    Emails IDs that we should not send to, for all accounts (global).
    """
    email_id = models.CharField(max_length=500, unique=True, db_index=True)
    added_on = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(null=True, blank=False, default=None)

    def __str__(self):
        return self.email_id


class CampaignActivity(models.Model):
    """
    Stores each event/activity for a campaign.
    """
    ACTIVITY_TYPES = [
        ('reply', "Reply"),
        ('unsubscribe', "Unsubscribe"),
    ]

    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE)

    event_type = models.CharField(max_length=100, choices=ACTIVITY_TYPES, null=False, blank=False, default="reply")
    event_subject = models.TextField(null=False, blank=False)
    event_from = models.TextField(null=False, blank=False)
    event_date = models.DateTimeField(auto_now_add=True)
    event_additional_data = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.id} - {self.event_type}"


class WarmupSchedule(models.Model):
    """
    Email warmup schedules. This should be deleted once warmup API request has been made successfully.
    """
    email = models.ForeignKey(EmailID, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

    schedule_datetime = models.DateTimeField(blank=False, null=False)

    def __str__(self):
        return f"{self.email.email_address} - {self.schedule_datetime.strftime('"%a, %d %b %Y %H:%M:%S %Z"')}"


# ============================================================
# ----------------------- INTEGRATIONS -----------------------
# ============================================================

class GooglePostmasterIntegration(models.Model):
    """
    "Active" integrations for an account.
    """
    # Use GooglePostmasterDomainSchema
    domains_added = models.JSONField(default=list)


# =============================================================
# ----------------------- MISCELLANEOUS -----------------------
# =============================================================

class SpamWord(models.Model):
    """

    """

    class Meta:
        ordering = ['name']

    name = models.CharField(max_length=500, blank=False, null=False)

    def __str__(self):
        return self.name


class FriendlySalutation(models.Model):
    """

    """

    class Meta:
        ordering = ['name']

    name = models.CharField(max_length=500, blank=False, null=False)

    def __str__(self):
        return self.name


class BlacklistCheckStatus(models.Model):
    """
    Blacklist Source of the connected Domain.
    """
    subdomain = models.ForeignKey("ManagedSubdomain", on_delete=models.CASCADE)
    checked_on = models.DateField(auto_now_add=True)
    blacklisted_sources = ArrayField(models.CharField(max_length=255), default=list, blank=True)

    class Meta:
        unique_together = ("subdomain", "checked_on")


class BlacklistAlertStatus(models.Model):
    """
    Tracks blacklist alert emails sent per domain.
    """
    subdomain = models.OneToOneField("ManagedSubdomain", on_delete=models.CASCADE)
    email_count = models.IntegerField(default=0)
    last_sent = models.DateField(null=True, blank=True)


class UpdateSection(models.Model):
    """
    Table to store Deliveryman Update Section.
    """
    title = models.CharField(max_length=500)
    description = models.TextField(max_length=2000)
    created_at = models.DateTimeField()

    def __str__(self):
        return f"{self.title}"


class EmailLinkClickEvent(models.Model):
    """
    We store all SNS link click events here for a given schedule.
    """
    schedule = models.ForeignKey(CampaignSchedule, on_delete=models.CASCADE)
    link = models.TextField(blank=False, null=False)
    clicked_on = models.DateTimeField()  # Needs to be fetched form SNS click timestamp.

    def __str__(self):
        return f"link click event: {self.id}"
